"""
SARA AI - Sistema de Reconhecimento de Voz
Captura e converte áudio em texto usando speech_recognition
"""

import speech_recognition as sr
import pyaudio
import threading
import time
from config import config
from src.logger import setup_logger

class VoiceRecognition:
    """Classe para reconhecimento de voz"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        self.is_listening = False
        
        # Configurar reconhecedor
        self._setup_recognizer()
        
    def _setup_recognizer(self):
        """Configura o reconhecedor de voz"""
        try:
            self.logger.info("Configurando reconhecedor de voz...")
            
            # Ajustar para ruído ambiente
            with self.microphone as source:
                self.logger.info("Calibrando para ruído ambiente...")
                self.recognizer.adjust_for_ambient_noise(source, duration=2)
                
            # Configurações do reconhecedor
            self.recognizer.energy_threshold = 300
            self.recognizer.dynamic_energy_threshold = True
            self.recognizer.pause_threshold = 0.8
            self.recognizer.phrase_threshold = 0.3
            
            self.logger.info("Reconhecedor configurado com sucesso")
            
        except Exception as e:
            self.logger.error(f"Erro ao configurar reconhecedor: {e}")
            raise
    
    def listen(self, timeout=None, phrase_time_limit=5):
        """
        Escuta por comandos de voz
        
        Args:
            timeout: Tempo limite para começar a escutar
            phrase_time_limit: Tempo limite para a frase
            
        Returns:
            str: Texto reconhecido ou None se não houver
        """
        try:
            self.logger.debug("Iniciando escuta...")
            
            with self.microphone as source:
                # Escutar áudio
                audio = self.recognizer.listen(
                    source, 
                    timeout=timeout, 
                    phrase_time_limit=phrase_time_limit
                )
                
            # Converter áudio para texto
            text = self.recognizer.recognize_google(
                audio, 
                language=config.VOICE_RECOGNITION_LANGUAGE
            )
            
            self.logger.debug(f"Texto reconhecido: {text}")
            return text.lower().strip()
            
        except sr.WaitTimeoutError:
            self.logger.debug("Timeout na escuta")
            return None
            
        except sr.UnknownValueError:
            self.logger.debug("Não foi possível entender o áudio")
            return None
            
        except sr.RequestError as e:
            self.logger.error(f"Erro no serviço de reconhecimento: {e}")
            return None
            
        except Exception as e:
            self.logger.error(f"Erro inesperado no reconhecimento: {e}")
            return None
    
    def listen_for_wake_word(self, wake_words=None):
        """
        Escuta continuamente por palavras de ativação
        
        Args:
            wake_words: Lista de palavras de ativação
            
        Returns:
            bool: True se palavra de ativação foi detectada
        """
        if wake_words is None:
            wake_words = config.AI_WAKE_WORDS
            
        try:
            text = self.listen(timeout=1, phrase_time_limit=3)
            
            if text:
                for wake_word in wake_words:
                    if wake_word in text:
                        self.logger.info(f"Palavra de ativação detectada: {wake_word}")
                        return True
                        
            return False
            
        except Exception as e:
            self.logger.error(f"Erro na detecção de palavra de ativação: {e}")
            return False
    
    def continuous_listen(self, callback, wake_words=None):
        """
        Escuta contínua com callback para processamento
        
        Args:
            callback: Função para processar texto reconhecido
            wake_words: Palavras de ativação (opcional)
        """
        self.is_listening = True
        
        def listen_loop():
            while self.is_listening:
                try:
                    # Se há palavras de ativação, escutar por elas primeiro
                    if wake_words:
                        if self.listen_for_wake_word(wake_words):
                            # Palavra de ativação detectada, escutar comando
                            text = self.listen(phrase_time_limit=10)
                            if text:
                                callback(text)
                    else:
                        # Escuta direta
                        text = self.listen()
                        if text:
                            callback(text)
                            
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.logger.error(f"Erro na escuta contínua: {e}")
                    time.sleep(1)
        
        # Iniciar thread de escuta
        listen_thread = threading.Thread(target=listen_loop, daemon=True)
        listen_thread.start()
        
        return listen_thread
    
    def stop_listening(self):
        """Para a escuta contínua"""
        self.is_listening = False
        self.logger.info("Escuta interrompida")
    
    def test_microphone(self):
        """Testa se o microfone está funcionando"""
        try:
            self.logger.info("Testando microfone...")
            
            with self.microphone as source:
                self.logger.info("Fale algo para testar o microfone...")
                audio = self.recognizer.listen(source, timeout=5, phrase_time_limit=3)
                
            text = self.recognizer.recognize_google(
                audio, 
                language=config.VOICE_RECOGNITION_LANGUAGE
            )
            
            self.logger.info(f"Teste bem-sucedido! Texto: {text}")
            return True, text
            
        except Exception as e:
            self.logger.error(f"Erro no teste do microfone: {e}")
            return False, str(e)
