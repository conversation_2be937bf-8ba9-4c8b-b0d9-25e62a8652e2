"""
SARA AI - Assistente de IA Conversacional
Arquivo principal para inicialização do sistema
"""

import sys
import threading
import time
from src.voice_recognition import VoiceRecognition
from src.voice_synthesis import VoiceSynthesis
from src.ai_brain import AIBrain
from src.system_controller import SystemController
from src.logger import setup_logger
from web.app import create_app, set_sara_instance
from config import config

class SaraAI:
    """Classe principal do assistente SARA AI"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.logger.info(f"Inicializando {config.APP_NAME} v{config.VERSION}")
        
        # Componentes principais
        self.voice_recognition = VoiceRecognition()
        self.voice_synthesis = VoiceSynthesis()
        self.ai_brain = AIBrain()
        self.system_controller = SystemController()
        
        # Estado do sistema
        self.is_running = False
        self.is_listening = False
        
        # Flask app para painel web
        self.web_app = create_app()

        # Conectar esta instância com o painel web
        set_sara_instance(self)
        
    def start(self):
        """Inicia o assistente SARA AI"""
        try:
            self.logger.info("Iniciando SARA AI...")
            self.is_running = True
            
            # Inicializar componentes
            self.voice_synthesis.speak("Olá querido! Eu sou a SARA, sua assistente pessoal! Estou aqui para facilitar sua vida!")
            self.logger.info("Sistema SARA AI iniciado com sucesso")

            # Iniciar servidor web em thread separada
            web_thread = threading.Thread(target=self._start_web_server, daemon=True)
            web_thread.start()

            # Loop principal de escuta
            self._main_loop()
            
        except KeyboardInterrupt:
            self.logger.info("Interrupção do usuário detectada")
            self.stop()
        except Exception as e:
            self.logger.error(f"Erro durante execução: {e}")
            self.stop()
    
    def stop(self):
        """Para o assistente SARA AI"""
        self.logger.info("Parando SARA AI...")
        self.is_running = False
        self.is_listening = False
        self.voice_synthesis.speak("Até logo!")
        sys.exit(0)
    
    def _start_web_server(self):
        """Inicia o servidor web Flask"""
        try:
            self.logger.info(f"Iniciando servidor web em http://{config.FLASK_HOST}:{config.FLASK_PORT}")
            self.web_app.run(
                host=config.FLASK_HOST,
                port=config.FLASK_PORT,
                debug=config.DEBUG,
                use_reloader=False  # Evita conflitos com threading
            )
        except Exception as e:
            self.logger.error(f"Erro no servidor web: {e}")
    
    def _main_loop(self):
        """Loop principal de escuta e processamento"""
        self.logger.info("Entrando no loop principal...")

        while self.is_running:
            try:
                # Escutar por comandos de voz
                if not self.is_listening:
                    self.is_listening = True
                    self.logger.debug("Aguardando comando de voz...")

                # Capturar áudio
                audio_text = self.voice_recognition.listen()

                if audio_text:
                    self.logger.info(f"Comando recebido: {audio_text}")

                    # Atualizar status no painel web
                    if hasattr(self.web_app, 'update_status'):
                        self.web_app.update_status({
                            'last_command': audio_text,
                            'is_listening': True
                        })

                    # Adicionar ao histórico do painel web
                    if hasattr(self.web_app, 'add_to_history'):
                        self.web_app.add_to_history({
                            'type': 'user',
                            'content': audio_text,
                            'timestamp': time.time(),
                            'source': 'voice'
                        })

                    # Verificar palavra de parada
                    if any(stop_word in audio_text.lower() for stop_word in config.AI_STOP_WORDS):
                        self.stop()
                        break

                    # Processar comando com IA
                    response = self.ai_brain.process_command(audio_text)

                    # Executar ação se necessário
                    if response.get('action'):
                        action_result = self.system_controller.execute_action(
                            response['action'],
                            response.get('parameters', {})
                        )

                        if action_result['success']:
                            self.logger.info(f"Ação executada: {response['action']}")
                        else:
                            self.logger.error(f"Erro na ação: {action_result['error']}")
                            response['text'] = f"Desculpe, não consegui executar essa ação: {action_result['error']}"

                    # Responder por voz
                    if response.get('text'):
                        self.voice_synthesis.speak(response['text'])

                        # Atualizar status no painel web
                        if hasattr(self.web_app, 'update_status'):
                            self.web_app.update_status({
                                'last_response': response['text']
                            })

                        # Adicionar resposta ao histórico do painel web
                        if hasattr(self.web_app, 'add_to_history'):
                            self.web_app.add_to_history({
                                'type': 'assistant',
                                'content': response['text'],
                                'timestamp': time.time(),
                                'source': 'voice'
                            })

                self.is_listening = False

                # Atualizar status no painel web
                if hasattr(self.web_app, 'update_status'):
                    self.web_app.update_status({
                        'is_listening': False,
                        'is_running': True
                    })

                time.sleep(0.1)  # Pequena pausa para evitar uso excessivo de CPU

            except Exception as e:
                self.logger.error(f"Erro no loop principal: {e}")
                time.sleep(1)  # Pausa maior em caso de erro

def main():
    """Função principal"""
    print(f"""
    ╔══════════════════════════════════════╗
    ║           SARA AI v{config.VERSION}              ║
    ║     Assistente de IA Conversacional   ║
    ╚══════════════════════════════════════╝
    """)
    
    # Criar e iniciar SARA AI
    sara = SaraAI()
    sara.start()

if __name__ == "__main__":
    main()
