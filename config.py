"""
SARA AI - Configurações do Sistema
Arquivo de configuração centralizado para o assistente de IA
"""

import os
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

class Config:
    """Configurações principais do sistema SARA AI"""
    
    # Configurações gerais
    APP_NAME = "SARA AI"
    VERSION = "1.0.0"
    DEBUG = True
    
    # Configurações de API
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
    OPENAI_MODEL = "gpt-3.5-turbo"
    
    # Configurações de voz
    VOICE_RECOGNITION_LANGUAGE = "pt-BR"
    VOICE_SYNTHESIS_LANGUAGE = "pt"
    VOICE_RATE = 150  # Velocidade da fala
    VOICE_VOLUME = 0.8  # Volume (0.0 a 1.0)
    
    # Configurações do servidor web
    FLASK_HOST = "127.0.0.1"
    FLASK_PORT = 5000
    FLASK_SECRET_KEY = os.getenv('FLASK_SECRET_KEY', 'sara-ai-secret-key-2024')
    
    # Configurações de logging
    LOG_LEVEL = "INFO"
    LOG_FILE = "logs/sara_ai.log"
    LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Configurações de sistema
    SYSTEM_COMMANDS = {
        "opera": {
            "command": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Opera\\opera.exe",
            "args": []
        },
        "chrome": {
            "command": "chrome.exe",
            "args": []
        },
        "notepad": {
            "command": "notepad.exe",
            "args": []
        },
        "calculator": {
            "command": "calc.exe",
            "args": []
        }
    }
    
    # Configurações de IA
    AI_PERSONALITY = """
    Você é SARA, um assistente de IA brasileiro amigável e prestativo.
    Você pode conversar naturalmente e executar comandos no computador.
    Seja sempre educada, clara e eficiente nas suas respostas.
    """
    
    AI_WAKE_WORDS = ["sara", "hey sara", "oi sara"]
    AI_STOP_WORDS = ["parar", "stop", "sair", "tchau"]
    
    # Configurações de segurança
    ALLOWED_COMMANDS = [
        "abrir", "fechar", "executar", "navegar", "pesquisar",
        "volume", "música", "vídeo", "jogo", "aplicativo"
    ]
    
    BLOCKED_COMMANDS = [
        "deletar", "remover", "formatar", "shutdown", "restart"
    ]

class DevelopmentConfig(Config):
    """Configurações para desenvolvimento"""
    DEBUG = True
    LOG_LEVEL = "DEBUG"

class ProductionConfig(Config):
    """Configurações para produção"""
    DEBUG = False
    LOG_LEVEL = "WARNING"

# Configuração ativa
config = DevelopmentConfig()
