"""
SARA AI - Cérebro de IA Conversacional
Processamento de linguagem natural e lógica de conversação
"""

import openai
import json
import re
from datetime import datetime
from config import config
from src.logger import setup_logger

class AIBrain:
    """Classe principal para processamento de IA conversacional"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.conversation_history = []
        self.user_context = {}
        
        # Configurar OpenAI
        self._setup_openai()
        
        # Comandos do sistema
        self.system_commands = {
            'abrir': self._parse_open_command,
            'fechar': self._parse_close_command,
            'executar': self._parse_execute_command,
            'navegar': self._parse_navigate_command,
            'pesquisar': self._parse_search_command,
            'volume': self._parse_volume_command,
            'música': self._parse_music_command,
            'vídeo': self._parse_video_command,
            'jogo': self._parse_game_command,
            'aplicativo': self._parse_app_command
        }
    
    def _setup_openai(self):
        """Configura a API do OpenAI"""
        try:
            if config.OPENAI_API_KEY:
                openai.api_key = config.OPENAI_API_KEY
                self.logger.info("OpenAI API configurada")
            else:
                self.logger.warning("Chave da OpenAI não configurada - usando respostas básicas")
                
        except Exception as e:
            self.logger.error(f"Erro ao configurar OpenAI: {e}")
    
    def process_command(self, user_input):
        """
        Processa comando do usuário e retorna resposta
        
        Args:
            user_input: Texto do usuário
            
        Returns:
            dict: Resposta com texto e ação (se houver)
        """
        try:
            self.logger.info(f"Processando comando: {user_input}")
            
            # Adicionar à história da conversa
            self._add_to_history("user", user_input)
            
            # Analisar se é um comando de sistema
            system_action = self._analyze_system_command(user_input)
            
            if system_action:
                # É um comando de sistema
                response_text = self._generate_system_response(system_action)
                response = {
                    'text': response_text,
                    'action': system_action['action'],
                    'parameters': system_action.get('parameters', {})
                }
            else:
                # É uma conversa normal
                response_text = self._generate_conversation_response(user_input)
                response = {
                    'text': response_text,
                    'action': None,
                    'parameters': {}
                }
            
            # Adicionar resposta à história
            self._add_to_history("assistant", response['text'])
            
            return response
            
        except Exception as e:
            self.logger.error(f"Erro ao processar comando: {e}")
            return {
                'text': "Desculpe, ocorreu um erro ao processar seu comando.",
                'action': None,
                'parameters': {}
            }
    
    def _analyze_system_command(self, user_input):
        """Analisa se o input é um comando de sistema"""
        user_input_lower = user_input.lower()
        
        # Verificar comandos conhecidos
        for command, parser in self.system_commands.items():
            if command in user_input_lower:
                try:
                    return parser(user_input_lower)
                except Exception as e:
                    self.logger.error(f"Erro ao analisar comando {command}: {e}")
                    continue
        
        return None
    
    def _parse_open_command(self, text):
        """Analisa comandos de 'abrir'"""
        if 'opera' in text:
            return {'action': 'open_application', 'parameters': {'app': 'opera'}}
        elif 'chrome' in text or 'navegador' in text:
            return {'action': 'open_application', 'parameters': {'app': 'chrome'}}
        elif 'notepad' in text or 'bloco de notas' in text:
            return {'action': 'open_application', 'parameters': {'app': 'notepad'}}
        elif 'calculadora' in text:
            return {'action': 'open_application', 'parameters': {'app': 'calculator'}}
        elif 'site' in text or 'página' in text:
            # Extrair URL se possível
            url_match = re.search(r'(https?://\S+|www\.\S+|\S+\.\S+)', text)
            url = url_match.group(1) if url_match else 'google.com'
            return {'action': 'open_website', 'parameters': {'url': url}}
        
        return None
    
    def _parse_close_command(self, text):
        """Analisa comandos de 'fechar'"""
        if 'opera' in text:
            return {'action': 'close_application', 'parameters': {'app': 'opera'}}
        elif 'chrome' in text:
            return {'action': 'close_application', 'parameters': {'app': 'chrome'}}
        elif 'aplicativo' in text or 'programa' in text:
            return {'action': 'close_active_window', 'parameters': {}}
        
        return None
    
    def _parse_execute_command(self, text):
        """Analisa comandos de 'executar'"""
        # Comando genérico de execução
        return {'action': 'execute_command', 'parameters': {'command': text}}
    
    def _parse_navigate_command(self, text):
        """Analisa comandos de navegação"""
        url_match = re.search(r'(https?://\S+|www\.\S+|\S+\.\S+)', text)
        url = url_match.group(1) if url_match else 'google.com'
        return {'action': 'open_website', 'parameters': {'url': url}}
    
    def _parse_search_command(self, text):
        """Analisa comandos de pesquisa"""
        # Extrair termo de pesquisa
        search_terms = ['pesquisar', 'procurar', 'buscar']
        for term in search_terms:
            if term in text:
                query = text.split(term, 1)[1].strip()
                return {'action': 'web_search', 'parameters': {'query': query}}
        
        return None
    
    def _parse_volume_command(self, text):
        """Analisa comandos de volume"""
        if 'aumentar' in text or 'subir' in text:
            return {'action': 'volume_up', 'parameters': {}}
        elif 'diminuir' in text or 'baixar' in text:
            return {'action': 'volume_down', 'parameters': {}}
        elif 'mudo' in text or 'silenciar' in text:
            return {'action': 'volume_mute', 'parameters': {}}
        
        return None
    
    def _parse_music_command(self, text):
        """Analisa comandos de música"""
        if 'tocar' in text or 'reproduzir' in text:
            return {'action': 'play_music', 'parameters': {}}
        elif 'pausar' in text or 'parar' in text:
            return {'action': 'pause_music', 'parameters': {}}
        
        return None
    
    def _parse_video_command(self, text):
        """Analisa comandos de vídeo"""
        if 'youtube' in text:
            return {'action': 'open_website', 'parameters': {'url': 'youtube.com'}}
        
        return None
    
    def _parse_game_command(self, text):
        """Analisa comandos de jogos"""
        # Jogos comuns do Windows
        games = {
            'solitaire': 'solitaire',
            'paciência': 'solitaire',
            'minesweeper': 'minesweeper',
            'campo minado': 'minesweeper'
        }
        
        for game_name, game_id in games.items():
            if game_name in text:
                return {'action': 'open_game', 'parameters': {'game': game_id}}
        
        return None
    
    def _parse_app_command(self, text):
        """Analisa comandos de aplicativo genérico"""
        return {'action': 'open_application', 'parameters': {'app': 'generic'}}
    
    def _generate_system_response(self, action):
        """Gera resposta para comandos de sistema"""
        action_type = action['action']
        
        responses = {
            'open_application': "Abrindo aplicativo...",
            'close_application': "Fechando aplicativo...",
            'open_website': "Abrindo site...",
            'web_search': "Realizando pesquisa...",
            'volume_up': "Aumentando volume...",
            'volume_down': "Diminuindo volume...",
            'volume_mute': "Silenciando áudio...",
            'play_music': "Reproduzindo música...",
            'pause_music': "Pausando música...",
            'open_game': "Abrindo jogo...",
            'execute_command': "Executando comando...",
            'close_active_window': "Fechando janela ativa..."
        }
        
        return responses.get(action_type, "Executando ação...")
    
    def _generate_conversation_response(self, user_input):
        """Gera resposta conversacional usando OpenAI ou respostas básicas"""
        try:
            if config.OPENAI_API_KEY:
                return self._generate_openai_response(user_input)
            else:
                return self._generate_basic_response(user_input)
                
        except Exception as e:
            self.logger.error(f"Erro ao gerar resposta: {e}")
            return "Desculpe, não consegui processar sua mensagem."
    
    def _generate_openai_response(self, user_input):
        """Gera resposta usando OpenAI API"""
        try:
            # Preparar mensagens para a API
            messages = [
                {"role": "system", "content": config.AI_PERSONALITY}
            ]
            
            # Adicionar histórico recente (últimas 10 mensagens)
            recent_history = self.conversation_history[-10:]
            for entry in recent_history:
                messages.append({
                    "role": entry["role"],
                    "content": entry["content"]
                })
            
            # Fazer chamada para OpenAI
            response = openai.ChatCompletion.create(
                model=config.OPENAI_MODEL,
                messages=messages,
                max_tokens=150,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            self.logger.error(f"Erro na API OpenAI: {e}")
            return self._generate_basic_response(user_input)
    
    def _generate_basic_response(self, user_input):
        """Gera respostas básicas sem IA externa"""
        user_input_lower = user_input.lower()
        
        # Respostas básicas
        if any(greeting in user_input_lower for greeting in ['oi', 'olá', 'hello', 'hey']):
            return "Olá! Como posso ajudá-lo hoje?"
        
        elif any(question in user_input_lower for question in ['como', 'o que', 'quando', 'onde']):
            return "Essa é uma boa pergunta! Posso ajudá-lo com comandos do sistema ou conversar sobre diversos assuntos."
        
        elif any(thanks in user_input_lower for thanks in ['obrigado', 'obrigada', 'valeu', 'thanks']):
            return "De nada! Estou aqui para ajudar sempre que precisar."
        
        elif any(bye in user_input_lower for bye in ['tchau', 'bye', 'até logo', 'adeus']):
            return "Até logo! Foi um prazer conversar com você."
        
        else:
            return "Entendi! Posso ajudá-lo com comandos do sistema como abrir aplicativos, navegar na web ou conversar sobre diversos assuntos."
    
    def _add_to_history(self, role, content):
        """Adiciona mensagem ao histórico da conversa"""
        self.conversation_history.append({
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        })
        
        # Manter apenas últimas 50 mensagens
        if len(self.conversation_history) > 50:
            self.conversation_history = self.conversation_history[-50:]
    
    def get_conversation_history(self):
        """Retorna histórico da conversa"""
        return self.conversation_history.copy()
    
    def clear_history(self):
        """Limpa histórico da conversa"""
        self.conversation_history.clear()
        self.logger.info("Histórico da conversa limpo")
    
    def set_user_context(self, key, value):
        """Define contexto do usuário"""
        self.user_context[key] = value
    
    def get_user_context(self, key, default=None):
        """Obtém contexto do usuário"""
        return self.user_context.get(key, default)
