"""
SARA AI - Cérebro de IA Conversacional (Versão Independente)
Processamento de linguagem natural e lógica de conversação sem APIs externas
"""

import json
import re
import random
from datetime import datetime
from config import config
from src.logger import setup_logger

# Tentar importar NLTK para processamento de texto (opcional)
try:
    import nltk
    from textblob import TextBlob
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

class AIBrain:
    """Classe principal para processamento de IA conversacional independente"""

    def __init__(self):
        self.logger = setup_logger(__name__)
        self.conversation_history = []
        self.user_context = {}

        # Inicializar processamento de texto local
        self._setup_local_nlp()

        # Comandos do sistema
        self.system_commands = {
            'abrir': self._parse_open_command,
            'fechar': self._parse_close_command,
            'executar': self._parse_execute_command,
            'navegar': self._parse_navigate_command,
            'pesquisar': self._parse_search_command,
            'volume': self._parse_volume_command,
            'música': self._parse_music_command,
            'vídeo': self._parse_video_command,
            'jogo': self._parse_game_command,
            'aplicativo': self._parse_app_command
        }

        # Base de conhecimento local para respostas
        self._setup_response_database()

    def _setup_local_nlp(self):
        """Configura processamento de linguagem natural local"""
        try:
            if NLTK_AVAILABLE:
                # Baixar dados necessários do NLTK (se não existirem)
                try:
                    nltk.data.find('tokenizers/punkt')
                except LookupError:
                    nltk.download('punkt', quiet=True)

                try:
                    nltk.data.find('corpora/stopwords')
                except LookupError:
                    nltk.download('stopwords', quiet=True)

                self.logger.info("NLTK configurado para processamento local")
            else:
                self.logger.info("NLTK não disponível - usando processamento básico")

        except Exception as e:
            self.logger.error(f"Erro ao configurar NLP local: {e}")

    def _setup_response_database(self):
        """Configura base de respostas local"""
        self.responses = {
            'greetings': [
                "Olá! Como posso ajudá-lo hoje?",
                "Oi! Em que posso ser útil?",
                "Olá! Estou aqui para ajudar!",
                "Oi! O que você gostaria de fazer?"
            ],
            'thanks': [
                "De nada! Estou sempre aqui para ajudar!",
                "Por nada! Foi um prazer ajudar!",
                "Disponha! Estou aqui quando precisar!",
                "Não há de quê! Sempre às ordens!"
            ],
            'goodbye': [
                "Até logo! Foi um prazer conversar!",
                "Tchau! Volte sempre que precisar!",
                "Até mais! Estarei aqui quando voltar!",
                "Adeus! Tenha um ótimo dia!"
            ],
            'questions': [
                "Essa é uma boa pergunta! Posso ajudá-lo com comandos do sistema ou conversar sobre diversos assuntos.",
                "Interessante! Posso executar comandos como abrir aplicativos, controlar volume ou navegar na web.",
                "Boa pergunta! Sou capaz de controlar seu computador e conversar naturalmente.",
                "Excelente pergunta! Posso ajudar com tarefas do sistema e manter uma conversa agradável."
            ],
            'compliments': [
                "Muito obrigada! Faço o meu melhor para ser útil!",
                "Que gentil! Adoro poder ajudar!",
                "Obrigada pelo elogio! Isso me motiva!",
                "Fico feliz que esteja gostando! Estou aqui para isso!"
            ],
            'default': [
                "Entendi! Posso ajudá-lo com comandos do sistema ou conversar sobre diversos assuntos.",
                "Interessante! Como posso ser útil hoje?",
                "Compreendo! Em que mais posso ajudar?",
                "Certo! Há algo específico que gostaria de fazer?"
            ]
        }
    
    def process_command(self, user_input):
        """
        Processa comando do usuário e retorna resposta
        
        Args:
            user_input: Texto do usuário
            
        Returns:
            dict: Resposta com texto e ação (se houver)
        """
        try:
            self.logger.info(f"Processando comando: {user_input}")
            
            # Adicionar à história da conversa
            self._add_to_history("user", user_input)
            
            # Analisar se é um comando de sistema
            system_action = self._analyze_system_command(user_input)
            
            if system_action:
                # É um comando de sistema
                response_text = self._generate_system_response(system_action)
                response = {
                    'text': response_text,
                    'action': system_action['action'],
                    'parameters': system_action.get('parameters', {})
                }
            else:
                # É uma conversa normal
                response_text = self._generate_conversation_response(user_input)
                response = {
                    'text': response_text,
                    'action': None,
                    'parameters': {}
                }
            
            # Adicionar resposta à história
            self._add_to_history("assistant", response['text'])
            
            return response
            
        except Exception as e:
            self.logger.error(f"Erro ao processar comando: {e}")
            return {
                'text': "Desculpe, ocorreu um erro ao processar seu comando.",
                'action': None,
                'parameters': {}
            }
    
    def _analyze_system_command(self, user_input):
        """Analisa se o input é um comando de sistema"""
        user_input_lower = user_input.lower()
        
        # Verificar comandos conhecidos
        for command, parser in self.system_commands.items():
            if command in user_input_lower:
                try:
                    return parser(user_input_lower)
                except Exception as e:
                    self.logger.error(f"Erro ao analisar comando {command}: {e}")
                    continue
        
        return None
    
    def _parse_open_command(self, text):
        """Analisa comandos de 'abrir'"""
        if 'opera' in text:
            return {'action': 'open_application', 'parameters': {'app': 'opera'}}
        elif 'chrome' in text or 'navegador' in text:
            return {'action': 'open_application', 'parameters': {'app': 'chrome'}}
        elif 'notepad' in text or 'bloco de notas' in text:
            return {'action': 'open_application', 'parameters': {'app': 'notepad'}}
        elif 'calculadora' in text:
            return {'action': 'open_application', 'parameters': {'app': 'calculator'}}
        elif 'site' in text or 'página' in text:
            # Extrair URL se possível
            url_match = re.search(r'(https?://\S+|www\.\S+|\S+\.\S+)', text)
            url = url_match.group(1) if url_match else 'google.com'
            return {'action': 'open_website', 'parameters': {'url': url}}
        
        return None
    
    def _parse_close_command(self, text):
        """Analisa comandos de 'fechar'"""
        if 'opera' in text:
            return {'action': 'close_application', 'parameters': {'app': 'opera'}}
        elif 'chrome' in text:
            return {'action': 'close_application', 'parameters': {'app': 'chrome'}}
        elif 'aplicativo' in text or 'programa' in text:
            return {'action': 'close_active_window', 'parameters': {}}
        
        return None
    
    def _parse_execute_command(self, text):
        """Analisa comandos de 'executar'"""
        # Comando genérico de execução
        return {'action': 'execute_command', 'parameters': {'command': text}}
    
    def _parse_navigate_command(self, text):
        """Analisa comandos de navegação"""
        url_match = re.search(r'(https?://\S+|www\.\S+|\S+\.\S+)', text)
        url = url_match.group(1) if url_match else 'google.com'
        return {'action': 'open_website', 'parameters': {'url': url}}
    
    def _parse_search_command(self, text):
        """Analisa comandos de pesquisa"""
        # Extrair termo de pesquisa
        search_terms = ['pesquisar', 'procurar', 'buscar']
        for term in search_terms:
            if term in text:
                query = text.split(term, 1)[1].strip()
                return {'action': 'web_search', 'parameters': {'query': query}}
        
        return None
    
    def _parse_volume_command(self, text):
        """Analisa comandos de volume"""
        if 'aumentar' in text or 'subir' in text:
            return {'action': 'volume_up', 'parameters': {}}
        elif 'diminuir' in text or 'baixar' in text:
            return {'action': 'volume_down', 'parameters': {}}
        elif 'mudo' in text or 'silenciar' in text:
            return {'action': 'volume_mute', 'parameters': {}}
        
        return None
    
    def _parse_music_command(self, text):
        """Analisa comandos de música"""
        if 'tocar' in text or 'reproduzir' in text:
            return {'action': 'play_music', 'parameters': {}}
        elif 'pausar' in text or 'parar' in text:
            return {'action': 'pause_music', 'parameters': {}}
        
        return None
    
    def _parse_video_command(self, text):
        """Analisa comandos de vídeo"""
        if 'youtube' in text:
            return {'action': 'open_website', 'parameters': {'url': 'youtube.com'}}
        
        return None
    
    def _parse_game_command(self, text):
        """Analisa comandos de jogos"""
        # Jogos comuns do Windows
        games = {
            'solitaire': 'solitaire',
            'paciência': 'solitaire',
            'minesweeper': 'minesweeper',
            'campo minado': 'minesweeper'
        }
        
        for game_name, game_id in games.items():
            if game_name in text:
                return {'action': 'open_game', 'parameters': {'game': game_id}}
        
        return None
    
    def _parse_app_command(self, text):
        """Analisa comandos de aplicativo genérico"""
        return {'action': 'open_application', 'parameters': {'app': 'generic'}}
    
    def _generate_system_response(self, action):
        """Gera resposta para comandos de sistema"""
        action_type = action['action']
        
        responses = {
            'open_application': "Abrindo aplicativo...",
            'close_application': "Fechando aplicativo...",
            'open_website': "Abrindo site...",
            'web_search': "Realizando pesquisa...",
            'volume_up': "Aumentando volume...",
            'volume_down': "Diminuindo volume...",
            'volume_mute': "Silenciando áudio...",
            'play_music': "Reproduzindo música...",
            'pause_music': "Pausando música...",
            'open_game': "Abrindo jogo...",
            'execute_command': "Executando comando...",
            'close_active_window': "Fechando janela ativa..."
        }
        
        return responses.get(action_type, "Executando ação...")
    
    def _generate_conversation_response(self, user_input):
        """Gera resposta conversacional usando processamento local"""
        try:
            return self._generate_intelligent_response(user_input)

        except Exception as e:
            self.logger.error(f"Erro ao gerar resposta: {e}")
            return "Desculpe, não consegui processar sua mensagem."

    def _generate_intelligent_response(self, user_input):
        """Gera resposta inteligente usando análise local"""
        try:
            user_input_lower = user_input.lower()

            # Análise de sentimento e contexto usando TextBlob (se disponível)
            if NLTK_AVAILABLE:
                try:
                    blob = TextBlob(user_input)
                    sentiment = blob.sentiment.polarity

                    # Ajustar resposta baseada no sentimento
                    if sentiment > 0.3:
                        response_modifier = " Fico feliz em ajudar!"
                    elif sentiment < -0.3:
                        response_modifier = " Vou fazer o meu melhor para resolver isso."
                    else:
                        response_modifier = ""

                except Exception:
                    response_modifier = ""
            else:
                response_modifier = ""

            # Detectar tipo de mensagem e responder adequadamente
            if any(greeting in user_input_lower for greeting in ['oi', 'olá', 'hello', 'hey', 'bom dia', 'boa tarde', 'boa noite']):
                response = random.choice(self.responses['greetings'])

            elif any(thanks in user_input_lower for thanks in ['obrigado', 'obrigada', 'valeu', 'thanks', 'brigado']):
                response = random.choice(self.responses['thanks'])

            elif any(bye in user_input_lower for bye in ['tchau', 'bye', 'até logo', 'adeus', 'falou']):
                response = random.choice(self.responses['goodbye'])

            elif any(question in user_input_lower for question in ['como', 'o que', 'quando', 'onde', 'por que', 'qual']):
                response = random.choice(self.responses['questions'])

            elif any(compliment in user_input_lower for compliment in ['legal', 'ótimo', 'bom', 'excelente', 'perfeito', 'incrível']):
                response = random.choice(self.responses['compliments'])

            elif 'nome' in user_input_lower or 'quem' in user_input_lower:
                response = f"Eu sou a {config.APP_NAME}, sua assistente de IA! Posso ajudá-lo com comandos do sistema e conversar naturalmente."

            elif 'tempo' in user_input_lower or 'clima' in user_input_lower:
                response = "Para informações sobre o tempo, posso abrir um site de meteorologia para você. Quer que eu faça isso?"

            elif 'hora' in user_input_lower or 'horas' in user_input_lower:
                from datetime import datetime
                now = datetime.now()
                response = f"Agora são {now.strftime('%H:%M')} de {now.strftime('%d/%m/%Y')}."

            elif 'ajuda' in user_input_lower or 'help' in user_input_lower:
                response = """Posso ajudá-lo com vários comandos:
                • "Abrir Opera" - Abre o navegador
                • "Pesquisar [termo]" - Busca no Google
                • "Volume up/down" - Controla volume
                • "Abrir calculadora" - Abre calculadora
                • E muito mais! Apenas me diga o que precisa."""

            else:
                response = random.choice(self.responses['default'])

            return response + response_modifier

        except Exception as e:
            self.logger.error(f"Erro na resposta inteligente: {e}")
            return self._generate_basic_response(user_input)
    
    def _generate_basic_response(self, user_input):
        """Gera respostas básicas sem IA externa"""
        user_input_lower = user_input.lower()
        
        # Respostas básicas
        if any(greeting in user_input_lower for greeting in ['oi', 'olá', 'hello', 'hey']):
            return "Olá! Como posso ajudá-lo hoje?"
        
        elif any(question in user_input_lower for question in ['como', 'o que', 'quando', 'onde']):
            return "Essa é uma boa pergunta! Posso ajudá-lo com comandos do sistema ou conversar sobre diversos assuntos."
        
        elif any(thanks in user_input_lower for thanks in ['obrigado', 'obrigada', 'valeu', 'thanks']):
            return "De nada! Estou aqui para ajudar sempre que precisar."
        
        elif any(bye in user_input_lower for bye in ['tchau', 'bye', 'até logo', 'adeus']):
            return "Até logo! Foi um prazer conversar com você."
        
        else:
            return "Entendi! Posso ajudá-lo com comandos do sistema como abrir aplicativos, navegar na web ou conversar sobre diversos assuntos."
    
    def _add_to_history(self, role, content):
        """Adiciona mensagem ao histórico da conversa"""
        self.conversation_history.append({
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        })
        
        # Manter apenas últimas 50 mensagens
        if len(self.conversation_history) > 50:
            self.conversation_history = self.conversation_history[-50:]
    
    def get_conversation_history(self):
        """Retorna histórico da conversa"""
        return self.conversation_history.copy()
    
    def clear_history(self):
        """Limpa histórico da conversa"""
        self.conversation_history.clear()
        self.logger.info("Histórico da conversa limpo")
    
    def set_user_context(self, key, value):
        """Define contexto do usuário"""
        self.user_context[key] = value
    
    def get_user_context(self, key, default=None):
        """Obtém contexto do usuário"""
        return self.user_context.get(key, default)
