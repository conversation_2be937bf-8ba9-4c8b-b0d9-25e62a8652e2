"""
SARA AI - Cérebro de IA Conversacional (Versão Independente)
Processamento de linguagem natural e lógica de conversação sem APIs externas
"""

import json
import re
import random
from datetime import datetime
from config import config
from src.logger import setup_logger

# Tentar importar NLTK para processamento de texto (opcional)
try:
    import nltk
    from textblob import TextBlob
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

class AIBrain:
    """Classe principal para processamento de IA conversacional independente"""

    def __init__(self):
        self.logger = setup_logger(__name__)
        self.conversation_history = []
        self.user_context = {}

        # Inicializar processamento de texto local
        self._setup_local_nlp()

        # Comandos do sistema
        self.system_commands = {
            'abrir': self._parse_open_command,
            'fechar': self._parse_close_command,
            'executar': self._parse_execute_command,
            'navegar': self._parse_navigate_command,
            'pesquisar': self._parse_search_command,
            'volume': self._parse_volume_command,
            'música': self._parse_music_command,
            'vídeo': self._parse_video_command,
            'jogo': self._parse_game_command,
            'aplicativo': self._parse_app_command
        }

        # Base de conhecimento local para respostas
        self._setup_response_database()

    def _setup_local_nlp(self):
        """Configura processamento de linguagem natural local"""
        try:
            if NLTK_AVAILABLE:
                # Baixar dados necessários do NLTK (se não existirem)
                try:
                    nltk.data.find('tokenizers/punkt')
                except LookupError:
                    nltk.download('punkt', quiet=True)

                try:
                    nltk.data.find('corpora/stopwords')
                except LookupError:
                    nltk.download('stopwords', quiet=True)

                self.logger.info("NLTK configurado para processamento local")
            else:
                self.logger.info("NLTK não disponível - usando processamento básico")

        except Exception as e:
            self.logger.error(f"Erro ao configurar NLP local: {e}")

    def _setup_response_database(self):
        """Configura base de respostas local"""
        self.responses = {
            'greetings': [
                "Olá querido! Como posso ajudá-lo hoje? Estou aqui para tornar seu dia mais fácil!",
                "Oi! Que bom te ver! Em que posso ser útil agora?",
                "Olá! Sou a SARA e estou muito feliz em ajudar você!",
                "Oi! Espero que esteja tendo um ótimo dia! O que podemos fazer juntos?"
            ],
            'thanks': [
                "Fico muito feliz em ajudar! É um prazer trabalhar com você!",
                "Que bom que pude ser útil! Estou sempre aqui quando precisar!",
                "Foi um prazer! Adoro quando consigo facilitar sua vida!",
                "Não precisa agradecer! Ajudar você é o que mais gosto de fazer!"
            ],
            'goodbye': [
                "Até logo! Foi maravilhoso conversar com você hoje!",
                "Tchau! Estarei aqui sempre que precisar de mim!",
                "Até mais! Espero te ver em breve! Tenha um dia incrível!",
                "Adeus! Cuide-se bem e volte sempre que quiser!"
            ],
            'questions': [
                "Que pergunta interessante! Posso ajudar com muitas coisas: abrir programas, pesquisar na internet, controlar o volume, e muito mais!",
                "Ótima pergunta! Sou especialista em facilitar sua vida digital. Posso executar comandos, abrir aplicativos e conversar sobre qualquer assunto!",
                "Adoro perguntas! Posso controlar seu computador, abrir sites, tocar música, e ainda manter uma conversa agradável com você!",
                "Excelente pergunta! Minha missão é ser sua assistente pessoal. Posso fazer desde tarefas simples até conversas mais complexas!"
            ],
            'compliments': [
                "Que gentil da sua parte! Fico realmente emocionada com elogios assim!",
                "Muito obrigada! Você é muito querido! Faço tudo com muito carinho!",
                "Que doce! Comentários assim me motivam a ser ainda melhor para você!",
                "Fico radiante com seu elogio! É maravilhoso saber que estou ajudando bem!"
            ],
            'confusion': [
                "Desculpe, não entendi muito bem. Pode repetir de forma diferente?",
                "Hmm, não captei direito. Pode me explicar melhor?",
                "Perdão, ficou um pouco confuso para mim. Pode reformular?",
                "Ops! Não compreendi completamente. Pode tentar de outro jeito?"
            ],
            'system_commands': [
                "Perfeito! Vou executar isso para você agora mesmo!",
                "Claro! Deixe comigo, já estou cuidando disso!",
                "Ótimo! Executando seu comando imediatamente!",
                "Com certeza! Vou fazer isso para você agora!"
            ],
            'default': [
                "Interessante! Como posso ajudar você com isso?",
                "Entendi! Há algo específico que gostaria que eu fizesse?",
                "Compreendo! De que forma posso ser mais útil?",
                "Certo! Vamos ver como posso facilitar isso para você!"
            ]
        }

        # Palavras-chave para melhor reconhecimento de intenções
        self.intent_keywords = {
            'greetings': ['oi', 'olá', 'hello', 'hey', 'bom dia', 'boa tarde', 'boa noite', 'e aí'],
            'thanks': ['obrigado', 'obrigada', 'valeu', 'thanks', 'brigado', 'vlw'],
            'goodbye': ['tchau', 'bye', 'até logo', 'adeus', 'falou', 'até mais'],
            'questions': ['como', 'o que', 'quando', 'onde', 'por que', 'qual', 'quem'],
            'compliments': ['legal', 'ótimo', 'bom', 'excelente', 'perfeito', 'incrível', 'massa', 'top'],
            'help': ['ajuda', 'help', 'socorro', 'não sei', 'como fazer'],
            'system': ['abrir', 'fechar', 'executar', 'rodar', 'iniciar', 'parar']
        }
    
    def process_command(self, user_input):
        """
        Processa comando do usuário e retorna resposta
        
        Args:
            user_input: Texto do usuário
            
        Returns:
            dict: Resposta com texto e ação (se houver)
        """
        try:
            self.logger.info(f"Processando comando: {user_input}")
            
            # Adicionar à história da conversa
            self._add_to_history("user", user_input)
            
            # Analisar se é um comando de sistema
            system_action = self._analyze_system_command(user_input)
            
            if system_action:
                # É um comando de sistema
                response_text = self._generate_system_response(system_action)
                response = {
                    'text': response_text,
                    'action': system_action['action'],
                    'parameters': system_action.get('parameters', {})
                }
            else:
                # É uma conversa normal
                response_text = self._generate_conversation_response(user_input)
                response = {
                    'text': response_text,
                    'action': None,
                    'parameters': {}
                }
            
            # Adicionar resposta à história
            self._add_to_history("assistant", response['text'])
            
            return response
            
        except Exception as e:
            self.logger.error(f"Erro ao processar comando: {e}")
            return {
                'text': "Desculpe, ocorreu um erro ao processar seu comando.",
                'action': None,
                'parameters': {}
            }
    
    def _analyze_system_command(self, user_input):
        """Analisa se o input é um comando de sistema"""
        user_input_lower = user_input.lower()
        
        # Verificar comandos conhecidos
        for command, parser in self.system_commands.items():
            if command in user_input_lower:
                try:
                    return parser(user_input_lower)
                except Exception as e:
                    self.logger.error(f"Erro ao analisar comando {command}: {e}")
                    continue
        
        return None
    
    def _parse_open_command(self, text):
        """Analisa comandos de 'abrir'"""
        if 'opera' in text:
            return {'action': 'open_application', 'parameters': {'app': 'opera'}}
        elif 'chrome' in text or 'navegador' in text:
            return {'action': 'open_application', 'parameters': {'app': 'chrome'}}
        elif 'notepad' in text or 'bloco de notas' in text:
            return {'action': 'open_application', 'parameters': {'app': 'notepad'}}
        elif 'calculadora' in text:
            return {'action': 'open_application', 'parameters': {'app': 'calculator'}}
        elif 'site' in text or 'página' in text:
            # Extrair URL se possível
            url_match = re.search(r'(https?://\S+|www\.\S+|\S+\.\S+)', text)
            url = url_match.group(1) if url_match else 'google.com'
            return {'action': 'open_website', 'parameters': {'url': url}}
        
        return None
    
    def _parse_close_command(self, text):
        """Analisa comandos de 'fechar'"""
        if 'opera' in text:
            return {'action': 'close_application', 'parameters': {'app': 'opera'}}
        elif 'chrome' in text:
            return {'action': 'close_application', 'parameters': {'app': 'chrome'}}
        elif 'aplicativo' in text or 'programa' in text:
            return {'action': 'close_active_window', 'parameters': {}}
        
        return None
    
    def _parse_execute_command(self, text):
        """Analisa comandos de 'executar'"""
        # Comando genérico de execução
        return {'action': 'execute_command', 'parameters': {'command': text}}
    
    def _parse_navigate_command(self, text):
        """Analisa comandos de navegação"""
        url_match = re.search(r'(https?://\S+|www\.\S+|\S+\.\S+)', text)
        url = url_match.group(1) if url_match else 'google.com'
        return {'action': 'open_website', 'parameters': {'url': url}}
    
    def _parse_search_command(self, text):
        """Analisa comandos de pesquisa"""
        # Extrair termo de pesquisa
        search_terms = ['pesquisar', 'procurar', 'buscar']
        for term in search_terms:
            if term in text:
                query = text.split(term, 1)[1].strip()
                return {'action': 'web_search', 'parameters': {'query': query}}
        
        return None
    
    def _parse_volume_command(self, text):
        """Analisa comandos de volume"""
        if 'aumentar' in text or 'subir' in text:
            return {'action': 'volume_up', 'parameters': {}}
        elif 'diminuir' in text or 'baixar' in text:
            return {'action': 'volume_down', 'parameters': {}}
        elif 'mudo' in text or 'silenciar' in text:
            return {'action': 'volume_mute', 'parameters': {}}
        
        return None
    
    def _parse_music_command(self, text):
        """Analisa comandos de música"""
        if 'tocar' in text or 'reproduzir' in text:
            return {'action': 'play_music', 'parameters': {}}
        elif 'pausar' in text or 'parar' in text:
            return {'action': 'pause_music', 'parameters': {}}
        
        return None
    
    def _parse_video_command(self, text):
        """Analisa comandos de vídeo"""
        if 'youtube' in text:
            return {'action': 'open_website', 'parameters': {'url': 'youtube.com'}}
        
        return None
    
    def _parse_game_command(self, text):
        """Analisa comandos de jogos"""
        # Jogos comuns do Windows
        games = {
            'solitaire': 'solitaire',
            'paciência': 'solitaire',
            'minesweeper': 'minesweeper',
            'campo minado': 'minesweeper'
        }
        
        for game_name, game_id in games.items():
            if game_name in text:
                return {'action': 'open_game', 'parameters': {'game': game_id}}
        
        return None
    
    def _parse_app_command(self, text):
        """Analisa comandos de aplicativo genérico"""
        return {'action': 'open_application', 'parameters': {'app': 'generic'}}
    
    def _generate_system_response(self, action):
        """Gera resposta para comandos de sistema"""
        action_type = action['action']
        
        responses = {
            'open_application': "Abrindo aplicativo...",
            'close_application': "Fechando aplicativo...",
            'open_website': "Abrindo site...",
            'web_search': "Realizando pesquisa...",
            'volume_up': "Aumentando volume...",
            'volume_down': "Diminuindo volume...",
            'volume_mute': "Silenciando áudio...",
            'play_music': "Reproduzindo música...",
            'pause_music': "Pausando música...",
            'open_game': "Abrindo jogo...",
            'execute_command': "Executando comando...",
            'close_active_window': "Fechando janela ativa..."
        }
        
        return responses.get(action_type, "Executando ação...")
    
    def _generate_conversation_response(self, user_input):
        """Gera resposta conversacional usando processamento local"""
        try:
            return self._generate_intelligent_response(user_input)

        except Exception as e:
            self.logger.error(f"Erro ao gerar resposta: {e}")
            return "Desculpe, não consegui processar sua mensagem."

    def _generate_intelligent_response(self, user_input):
        """Gera resposta inteligente usando análise local melhorada"""
        try:
            user_input_lower = user_input.lower().strip()

            # Análise de sentimento e contexto usando TextBlob (se disponível)
            sentiment_modifier = ""
            if NLTK_AVAILABLE:
                try:
                    blob = TextBlob(user_input)
                    sentiment = blob.sentiment.polarity

                    # Ajustar resposta baseada no sentimento
                    if sentiment > 0.3:
                        sentiment_modifier = " Que bom que está animado!"
                    elif sentiment < -0.3:
                        sentiment_modifier = " Vou fazer o meu melhor para ajudar você!"

                except Exception:
                    pass

            # Detectar intenção usando palavras-chave melhoradas
            detected_intent = self._detect_intent(user_input_lower)

            # Gerar resposta baseada na intenção
            if detected_intent == 'greetings':
                response = random.choice(self.responses['greetings'])

            elif detected_intent == 'thanks':
                response = random.choice(self.responses['thanks'])

            elif detected_intent == 'goodbye':
                response = random.choice(self.responses['goodbye'])

            elif detected_intent == 'questions':
                response = random.choice(self.responses['questions'])

            elif detected_intent == 'compliments':
                response = random.choice(self.responses['compliments'])

            elif detected_intent == 'help':
                response = self._generate_help_response()

            elif detected_intent == 'system':
                response = random.choice(self.responses['system_commands'])

            # Respostas específicas para perguntas comuns
            elif any(word in user_input_lower for word in ['nome', 'quem é você', 'quem você é']):
                response = f"Eu sou a SARA, sua assistente pessoal! Sou uma IA criada para facilitar sua vida digital. Posso conversar, executar comandos e ajudar com diversas tarefas!"

            elif any(word in user_input_lower for word in ['tempo', 'clima', 'previsão']):
                response = "Que tal eu abrir um site de meteorologia para você? Posso fazer isso agora mesmo!"

            elif any(word in user_input_lower for word in ['hora', 'horas', 'que horas']):
                from datetime import datetime
                now = datetime.now()
                hora_formatada = now.strftime('%H:%M')
                data_formatada = now.strftime('%d de %B de %Y')
                response = f"Agora são {hora_formatada} do dia {data_formatada}. Posso ajudar com mais alguma coisa?"

            elif any(word in user_input_lower for word in ['música', 'tocar', 'som']):
                response = "Posso controlar a reprodução de música para você! Quer que eu toque, pause ou ajuste o volume?"

            elif any(word in user_input_lower for word in ['internet', 'site', 'navegar']):
                response = "Claro! Posso abrir qualquer site para você. Qual site gostaria de visitar?"

            elif any(word in user_input_lower for word in ['como você', 'como está', 'tudo bem']):
                response = "Estou ótima, obrigada por perguntar! Sempre feliz em ajudar você. E você, como está?"

            elif len(user_input_lower) <= 3:  # Respostas muito curtas
                response = random.choice(self.responses['confusion'])

            else:
                response = random.choice(self.responses['default'])

            return response + sentiment_modifier

        except Exception as e:
            self.logger.error(f"Erro na resposta inteligente: {e}")
            return "Desculpe, tive um pequeno problema para processar isso. Pode tentar novamente?"

    def _detect_intent(self, user_input):
        """Detecta a intenção do usuário baseada em palavras-chave"""
        for intent, keywords in self.intent_keywords.items():
            if any(keyword in user_input for keyword in keywords):
                return intent
        return 'default'

    def _generate_help_response(self):
        """Gera resposta de ajuda personalizada"""
        return """Claro! Posso ajudar com muitas coisas:

🖥️ **Comandos de Sistema:**
• "Abrir Opera" - Abre o navegador
• "Abrir calculadora" - Abre a calculadora
• "Volume up/down" - Controla o volume

🌐 **Internet:**
• "Pesquisar [termo]" - Busca no Google
• "Abrir site [nome]" - Navega para um site

🎵 **Mídia:**
• "Tocar música" - Controla reprodução
• "Pausar" - Para a música

💬 **Conversa:**
• Posso conversar sobre qualquer assunto!
• Pergunte sobre hora, clima, ou qualquer coisa!

Apenas me diga o que precisa e eu cuido do resto!"""
    
    def _generate_basic_response(self, user_input):
        """Gera respostas básicas sem IA externa"""
        user_input_lower = user_input.lower()
        
        # Respostas básicas
        if any(greeting in user_input_lower for greeting in ['oi', 'olá', 'hello', 'hey']):
            return "Olá! Como posso ajudá-lo hoje?"
        
        elif any(question in user_input_lower for question in ['como', 'o que', 'quando', 'onde']):
            return "Essa é uma boa pergunta! Posso ajudá-lo com comandos do sistema ou conversar sobre diversos assuntos."
        
        elif any(thanks in user_input_lower for thanks in ['obrigado', 'obrigada', 'valeu', 'thanks']):
            return "De nada! Estou aqui para ajudar sempre que precisar."
        
        elif any(bye in user_input_lower for bye in ['tchau', 'bye', 'até logo', 'adeus']):
            return "Até logo! Foi um prazer conversar com você."
        
        else:
            return "Entendi! Posso ajudá-lo com comandos do sistema como abrir aplicativos, navegar na web ou conversar sobre diversos assuntos."
    
    def _add_to_history(self, role, content):
        """Adiciona mensagem ao histórico da conversa"""
        self.conversation_history.append({
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        })
        
        # Manter apenas últimas 50 mensagens
        if len(self.conversation_history) > 50:
            self.conversation_history = self.conversation_history[-50:]
    
    def get_conversation_history(self):
        """Retorna histórico da conversa"""
        return self.conversation_history.copy()
    
    def clear_history(self):
        """Limpa histórico da conversa"""
        self.conversation_history.clear()
        self.logger.info("Histórico da conversa limpo")
    
    def set_user_context(self, key, value):
        """Define contexto do usuário"""
        self.user_context[key] = value
    
    def get_user_context(self, key, default=None):
        """Obtém contexto do usuário"""
        return self.user_context.get(key, default)
