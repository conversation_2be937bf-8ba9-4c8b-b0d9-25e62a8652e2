<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SARA AI - Painel de Controle</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-robot"></i>
                    <h1>SARA AI</h1>
                    <span class="version">v1.0.0</span>
                </div>
                <div class="status-indicator">
                    <div class="status-dot" id="statusDot"></div>
                    <span id="statusText">Desconectado</span>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Dashboard Cards -->
            <section class="dashboard">
                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-microphone"></i>
                        <h3>Status de Voz</h3>
                    </div>
                    <div class="card-content">
                        <div class="status-item">
                            <span>Escutando:</span>
                            <span id="listeningStatus" class="status-value">Não</span>
                        </div>
                        <div class="status-item">
                            <span>Último Comando:</span>
                            <span id="lastCommand" class="status-value">Nenhum</span>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-brain"></i>
                        <h3>IA Status</h3>
                    </div>
                    <div class="card-content">
                        <div class="status-item">
                            <span>Processando:</span>
                            <span id="processingStatus" class="status-value">Não</span>
                        </div>
                        <div class="status-item">
                            <span>Última Resposta:</span>
                            <span id="lastResponse" class="status-value">Nenhuma</span>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <i class="fas fa-desktop"></i>
                        <h3>Sistema</h3>
                    </div>
                    <div class="card-content">
                        <div class="status-item">
                            <span>CPU:</span>
                            <span id="cpuUsage" class="status-value">0%</span>
                        </div>
                        <div class="status-item">
                            <span>Memória:</span>
                            <span id="memoryUsage" class="status-value">0%</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Control Panel -->
            <section class="control-panel">
                <div class="card full-width">
                    <div class="card-header">
                        <i class="fas fa-terminal"></i>
                        <h3>Painel de Comando</h3>
                    </div>
                    <div class="card-content">
                        <div class="command-input">
                            <input type="text" id="commandInput" placeholder="Digite um comando para SARA..." />
                            <button id="sendCommand" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                                Enviar
                            </button>
                        </div>
                        <div class="quick-commands">
                            <button class="btn btn-secondary" onclick="sendQuickCommand('abrir opera')">
                                <i class="fab fa-opera"></i> Abrir Opera
                            </button>
                            <button class="btn btn-secondary" onclick="sendQuickCommand('abrir calculadora')">
                                <i class="fas fa-calculator"></i> Calculadora
                            </button>
                            <button class="btn btn-secondary" onclick="sendQuickCommand('pesquisar clima')">
                                <i class="fas fa-cloud-sun"></i> Clima
                            </button>
                            <button class="btn btn-secondary" onclick="sendQuickCommand('volume up')">
                                <i class="fas fa-volume-up"></i> Volume +
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Conversation History -->
            <section class="conversation">
                <div class="card full-width">
                    <div class="card-header">
                        <i class="fas fa-comments"></i>
                        <h3>Histórico de Conversas</h3>
                        <button id="clearHistory" class="btn btn-small">
                            <i class="fas fa-trash"></i> Limpar
                        </button>
                    </div>
                    <div class="card-content">
                        <div id="conversationHistory" class="conversation-history">
                            <div class="conversation-item system">
                                <div class="message-content">
                                    <span class="timestamp">Sistema</span>
                                    <p>SARA AI iniciada e pronta para uso!</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Configuration -->
            <section class="configuration">
                <div class="card full-width">
                    <div class="card-header">
                        <i class="fas fa-cog"></i>
                        <h3>Configurações</h3>
                    </div>
                    <div class="card-content">
                        <div class="config-section">
                            <h4>Voz</h4>
                            <div class="config-item">
                                <label for="voiceRate">Velocidade da Fala:</label>
                                <input type="range" id="voiceRate" min="50" max="300" value="150">
                                <span id="voiceRateValue">150</span>
                            </div>
                            <div class="config-item">
                                <label for="voiceVolume">Volume:</label>
                                <input type="range" id="voiceVolume" min="0" max="100" value="80">
                                <span id="voiceVolumeValue">80%</span>
                            </div>
                        </div>
                        
                        <div class="config-section">
                            <h4>Testes</h4>
                            <div class="test-buttons">
                                <button id="testSynthesis" class="btn btn-secondary">
                                    <i class="fas fa-volume-up"></i> Testar Síntese
                                </button>
                                <button id="testRecognition" class="btn btn-secondary">
                                    <i class="fas fa-microphone"></i> Testar Reconhecimento
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Logs -->
            <section class="logs">
                <div class="card full-width">
                    <div class="card-header">
                        <i class="fas fa-file-alt"></i>
                        <h3>Logs do Sistema</h3>
                        <button id="refreshLogs" class="btn btn-small">
                            <i class="fas fa-sync"></i> Atualizar
                        </button>
                    </div>
                    <div class="card-content">
                        <div id="systemLogs" class="logs-container">
                            <div class="log-entry info">
                                <span class="log-time">00:00:00</span>
                                <span class="log-level">INFO</span>
                                <span class="log-message">Sistema iniciado</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
