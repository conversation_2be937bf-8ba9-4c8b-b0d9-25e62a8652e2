"""
SARA AI - Cérebro de IA Avançado com Aprendizado
Sistema inteligente que aprende com interações e pode ser treinado
"""

import json
import re
import random
import sqlite3
import os
import pickle
from datetime import datetime
from collections import defaultdict, Counter
from config import config
from src.logger import setup_logger

# Tentar importar bibliotecas de ML
try:
    import numpy as np
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.metrics.pairwise import cosine_similarity
    from sklearn.naive_bayes import MultinomialNB
    from sklearn.pipeline import Pipeline
    import nltk
    from textblob import TextBlob
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False

class AdvancedAIBrain:
    """Cérebro de IA avançado com capacidade de aprendizado"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.conversation_history = []
        self.user_context = {}
        
        # Base de dados para aprendizado
        self.db_path = "data/sara_brain.db"
        self.knowledge_base = {}
        self.user_patterns = defaultdict(list)
        self.command_success_rate = defaultdict(float)
        
        # Modelos de ML
        self.intent_classifier = None
        self.response_vectorizer = None
        self.similarity_threshold = 0.7
        
        # Inicializar sistema
        self._setup_database()
        self._load_knowledge_base()
        self._setup_ml_models()
        self._setup_learning_system()
        
        self.logger.info(f"IA Avançada inicializada (ML: {'Sim' if ML_AVAILABLE else 'Não'})")
    
    def _setup_database(self):
        """Configura base de dados SQLite para aprendizado"""
        try:
            os.makedirs("data", exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Tabela de interações
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS interactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_input TEXT NOT NULL,
                    ai_response TEXT NOT NULL,
                    intent TEXT,
                    success_rating INTEGER DEFAULT 0,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    context TEXT
                )
            ''')
            
            # Tabela de comandos aprendidos
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learned_commands (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    command_pattern TEXT NOT NULL,
                    action_type TEXT NOT NULL,
                    parameters TEXT,
                    success_count INTEGER DEFAULT 0,
                    failure_count INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tabela de correções do usuário
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_corrections (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    original_input TEXT NOT NULL,
                    expected_response TEXT NOT NULL,
                    actual_response TEXT NOT NULL,
                    correction_type TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
            self.logger.info("Base de dados configurada")
            
        except Exception as e:
            self.logger.error(f"Erro ao configurar base de dados: {e}")
    
    def _load_knowledge_base(self):
        """Carrega base de conhecimento expandida"""
        self.knowledge_base = {
            'greetings': {
                'patterns': ['oi', 'olá', 'hello', 'hey', 'bom dia', 'boa tarde', 'boa noite', 'e aí', 'salve'],
                'responses': [
                    "Olá querido! Como posso ajudá-lo hoje? Estou aqui para facilitar sua vida!",
                    "Oi! Que alegria te ver! Em que posso ser útil agora?",
                    "Olá! Sou a SARA e estou muito feliz em conversar com você!",
                    "Oi! Espero que esteja tendo um dia maravilhoso! Como posso ajudar?"
                ]
            },
            'thanks': {
                'patterns': ['obrigado', 'obrigada', 'valeu', 'thanks', 'brigado', 'vlw', 'muito obrigado'],
                'responses': [
                    "Fico muito feliz em ajudar! É um prazer trabalhar com você!",
                    "Que bom que pude ser útil! Estou sempre aqui quando precisar!",
                    "Foi um prazer! Adoro quando consigo facilitar sua vida!",
                    "Não precisa agradecer! Ajudar você é o que mais gosto de fazer!"
                ]
            },
            'questions': {
                'patterns': ['como', 'o que', 'quando', 'onde', 'por que', 'qual', 'quem', 'quanto'],
                'responses': [
                    "Que pergunta interessante! Vou fazer o meu melhor para responder.",
                    "Ótima pergunta! Deixe-me pensar na melhor resposta para você.",
                    "Adoro perguntas! Vou buscar a informação que você precisa.",
                    "Excelente pergunta! Vou te ajudar com isso."
                ]
            },
            'system_commands': {
                'patterns': ['abrir', 'fechar', 'executar', 'rodar', 'iniciar', 'parar', 'ligar', 'desligar'],
                'responses': [
                    "Perfeito! Vou executar isso para você agora mesmo!",
                    "Claro! Deixe comigo, já estou cuidando disso!",
                    "Ótimo! Executando seu comando imediatamente!",
                    "Com certeza! Vou fazer isso para você agora!"
                ]
            },
            'learning': {
                'patterns': ['aprenda', 'ensinar', 'treinar', 'lembrar', 'salvar', 'guardar'],
                'responses': [
                    "Entendi! Vou aprender isso e lembrar para a próxima vez!",
                    "Perfeito! Salvando essa informação na minha memória!",
                    "Ótimo! Agora sei como fazer isso melhor!",
                    "Obrigada por me ensinar! Vou lembrar disso!"
                ]
            }
        }
    
    def _setup_ml_models(self):
        """Configura modelos de machine learning"""
        if not ML_AVAILABLE:
            self.logger.warning("Bibliotecas de ML não disponíveis - usando sistema básico")
            return
        
        try:
            # Baixar dados do NLTK se necessário
            try:
                nltk.data.find('tokenizers/punkt')
            except LookupError:
                nltk.download('punkt', quiet=True)
            
            try:
                nltk.data.find('corpora/stopwords')
            except LookupError:
                nltk.download('stopwords', quiet=True)
            
            # Configurar classificador de intenções
            self.intent_classifier = Pipeline([
                ('tfidf', TfidfVectorizer(max_features=1000, stop_words='english')),
                ('classifier', MultinomialNB())
            ])
            
            # Configurar vetorizador para similaridade
            self.response_vectorizer = TfidfVectorizer(max_features=500)
            
            # Treinar com dados existentes
            self._train_initial_models()
            
            self.logger.info("Modelos de ML configurados")
            
        except Exception as e:
            self.logger.error(f"Erro ao configurar ML: {e}")
    
    def _setup_learning_system(self):
        """Configura sistema de aprendizado contínuo"""
        self.learning_enabled = True
        self.auto_improve = True
        self.feedback_threshold = 3  # Mínimo de feedbacks para aprender
        
        # Carregar padrões aprendidos
        self._load_learned_patterns()
    
    def _train_initial_models(self):
        """Treina modelos iniciais com dados base"""
        if not ML_AVAILABLE:
            return
        
        try:
            # Dados de treinamento inicial
            training_data = []
            training_labels = []
            
            for intent, data in self.knowledge_base.items():
                for pattern in data['patterns']:
                    training_data.append(pattern)
                    training_labels.append(intent)
            
            if training_data:
                self.intent_classifier.fit(training_data, training_labels)
                self.logger.info(f"Modelo treinado com {len(training_data)} exemplos")
            
        except Exception as e:
            self.logger.error(f"Erro no treinamento inicial: {e}")
    
    def _load_learned_patterns(self):
        """Carrega padrões aprendidos da base de dados"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT command_pattern, action_type, parameters, success_count, failure_count
                FROM learned_commands
                WHERE success_count > failure_count
            ''')
            
            learned_commands = cursor.fetchall()
            
            for pattern, action_type, params, success, failure in learned_commands:
                confidence = success / (success + failure) if (success + failure) > 0 else 0
                if confidence > 0.6:  # Só usar comandos com boa taxa de sucesso
                    self.user_patterns[action_type].append({
                        'pattern': pattern,
                        'parameters': json.loads(params) if params else {},
                        'confidence': confidence
                    })
            
            conn.close()
            self.logger.info(f"Carregados {len(learned_commands)} padrões aprendidos")
            
        except Exception as e:
            self.logger.error(f"Erro ao carregar padrões: {e}")
    
    def process_command(self, user_input):
        """Processa comando com IA avançada e aprendizado"""
        try:
            self.logger.info(f"Processando comando avançado: {user_input}")
            
            # Adicionar à história
            self._add_to_history("user", user_input)
            
            # Detectar intenção usando ML (se disponível)
            intent = self._detect_intent_ml(user_input) if ML_AVAILABLE else self._detect_intent_basic(user_input)
            
            # Analisar comando para ações de sistema
            system_action = self._analyze_system_command(user_input)
            
            if system_action:
                # É um comando de sistema
                response_text = self._generate_system_response(system_action, intent)
                response = {
                    'text': response_text,
                    'action': system_action['action'],
                    'parameters': system_action.get('parameters', {}),
                    'intent': intent,
                    'confidence': system_action.get('confidence', 0.8)
                }
            else:
                # É uma conversa normal
                response_text = self._generate_intelligent_response(user_input, intent)
                response = {
                    'text': response_text,
                    'action': None,
                    'parameters': {},
                    'intent': intent,
                    'confidence': 0.9
                }
            
            # Salvar interação para aprendizado
            self._save_interaction(user_input, response)
            
            # Adicionar resposta à história
            self._add_to_history("assistant", response['text'])
            
            return response
            
        except Exception as e:
            self.logger.error(f"Erro ao processar comando avançado: {e}")
            return {
                'text': "Desculpe, tive um problema para processar isso. Pode tentar de novo?",
                'action': None,
                'parameters': {},
                'intent': 'error',
                'confidence': 0.0
            }
    
    def _detect_intent_ml(self, user_input):
        """Detecta intenção usando machine learning"""
        try:
            if self.intent_classifier:
                predicted_intent = self.intent_classifier.predict([user_input])[0]
                confidence = max(self.intent_classifier.predict_proba([user_input])[0])
                
                if confidence > self.similarity_threshold:
                    return predicted_intent
            
            # Fallback para detecção básica
            return self._detect_intent_basic(user_input)
            
        except Exception as e:
            self.logger.error(f"Erro na detecção ML: {e}")
            return self._detect_intent_basic(user_input)
    
    def _detect_intent_basic(self, user_input):
        """Detecção básica de intenção"""
        user_input_lower = user_input.lower()
        
        for intent, data in self.knowledge_base.items():
            for pattern in data['patterns']:
                if pattern in user_input_lower:
                    return intent
        
        return 'default'
    
    def _generate_intelligent_response(self, user_input, intent):
        """Gera resposta inteligente baseada na intenção"""
        try:
            # Buscar resposta na base de conhecimento
            if intent in self.knowledge_base:
                base_response = random.choice(self.knowledge_base[intent]['responses'])
            else:
                base_response = "Interessante! Como posso ajudar você com isso?"
            
            # Personalizar resposta baseada no contexto
            personalized_response = self._personalize_response(base_response, user_input, intent)
            
            return personalized_response
            
        except Exception as e:
            self.logger.error(f"Erro na resposta inteligente: {e}")
            return "Desculpe, não consegui processar isso adequadamente."
    
    def _personalize_response(self, base_response, user_input, intent):
        """Personaliza resposta baseada no contexto e histórico"""
        try:
            # Análise de sentimento se disponível
            if ML_AVAILABLE:
                try:
                    blob = TextBlob(user_input)
                    sentiment = blob.sentiment.polarity
                    
                    if sentiment > 0.3:
                        base_response += " Que bom que está animado!"
                    elif sentiment < -0.3:
                        base_response += " Vou fazer o meu melhor para ajudar!"
                except:
                    pass
            
            # Adicionar contexto baseado no histórico
            if len(self.conversation_history) > 2:
                recent_topics = [entry['content'] for entry in self.conversation_history[-3:]]
                # Aqui poderia adicionar lógica para manter contexto da conversa
            
            return base_response
            
        except Exception as e:
            self.logger.error(f"Erro na personalização: {e}")
            return base_response
    
    def learn_from_correction(self, original_input, expected_response, correction_type="user_feedback"):
        """Aprende com correções do usuário"""
        try:
            # Salvar correção na base de dados
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO user_corrections (original_input, expected_response, actual_response, correction_type)
                VALUES (?, ?, ?, ?)
            ''', (original_input, expected_response, "previous_response", correction_type))
            
            conn.commit()
            conn.close()
            
            # Retreinar modelo se necessário
            if ML_AVAILABLE:
                self._retrain_with_feedback()
            
            self.logger.info(f"Aprendizado registrado: {original_input} -> {expected_response}")
            return True
            
        except Exception as e:
            self.logger.error(f"Erro no aprendizado: {e}")
            return False
    
    def add_custom_command(self, command_pattern, action_type, parameters=None):
        """Adiciona comando personalizado"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO learned_commands (command_pattern, action_type, parameters)
                VALUES (?, ?, ?)
            ''', (command_pattern, action_type, json.dumps(parameters) if parameters else None))
            
            conn.commit()
            conn.close()
            
            # Atualizar padrões em memória
            self.user_patterns[action_type].append({
                'pattern': command_pattern,
                'parameters': parameters or {},
                'confidence': 1.0
            })
            
            self.logger.info(f"Comando personalizado adicionado: {command_pattern}")
            return True
            
        except Exception as e:
            self.logger.error(f"Erro ao adicionar comando: {e}")
            return False
    
    def get_learning_stats(self):
        """Retorna estatísticas de aprendizado"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Contar interações
            cursor.execute('SELECT COUNT(*) FROM interactions')
            total_interactions = cursor.fetchone()[0]
            
            # Contar comandos aprendidos
            cursor.execute('SELECT COUNT(*) FROM learned_commands')
            learned_commands = cursor.fetchone()[0]
            
            # Contar correções
            cursor.execute('SELECT COUNT(*) FROM user_corrections')
            corrections = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'total_interactions': total_interactions,
                'learned_commands': learned_commands,
                'user_corrections': corrections,
                'ml_available': ML_AVAILABLE,
                'learning_enabled': self.learning_enabled
            }
            
        except Exception as e:
            self.logger.error(f"Erro ao obter estatísticas: {e}")
            return {}


    def _save_interaction(self, user_input, response):
        """Salva interação para aprendizado futuro"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO interactions (user_input, ai_response, intent, context)
                VALUES (?, ?, ?, ?)
            ''', (user_input, response['text'], response.get('intent', 'unknown'),
                  json.dumps(self.user_context)))

            conn.commit()
            conn.close()

        except Exception as e:
            self.logger.error(f"Erro ao salvar interação: {e}")

    def _retrain_with_feedback(self):
        """Retreina modelo com feedback do usuário"""
        if not ML_AVAILABLE:
            return

        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Buscar correções recentes
            cursor.execute('''
                SELECT original_input, expected_response FROM user_corrections
                ORDER BY timestamp DESC LIMIT 100
            ''')

            corrections = cursor.fetchall()
            conn.close()

            if corrections:
                # Aqui você poderia implementar retreinamento mais sofisticado
                self.logger.info(f"Retreinamento com {len(corrections)} correções")

        except Exception as e:
            self.logger.error(f"Erro no retreinamento: {e}")

    def _analyze_system_command(self, user_input):
        """Analisa se é um comando de sistema (versão melhorada)"""
        user_input_lower = user_input.lower()

        # Comandos básicos do sistema original
        system_commands = {
            'abrir': self._parse_open_command,
            'fechar': self._parse_close_command,
            'executar': self._parse_execute_command,
            'navegar': self._parse_navigate_command,
            'pesquisar': self._parse_search_command,
            'volume': self._parse_volume_command,
            'música': self._parse_music_command,
            'vídeo': self._parse_video_command,
            'jogo': self._parse_game_command,
            'aplicativo': self._parse_app_command
        }

        # Verificar comandos aprendidos primeiro
        for action_type, patterns in self.user_patterns.items():
            for pattern_data in patterns:
                if pattern_data['pattern'].lower() in user_input_lower:
                    return {
                        'action': action_type,
                        'parameters': pattern_data['parameters'],
                        'confidence': pattern_data['confidence']
                    }

        # Verificar comandos básicos
        for command, parser in system_commands.items():
            if command in user_input_lower:
                try:
                    result = parser(user_input_lower)
                    if result:
                        result['confidence'] = 0.8
                        return result
                except Exception as e:
                    self.logger.error(f"Erro ao analisar comando {command}: {e}")

        return None

    # Métodos de parsing (mantidos do sistema original)
    def _parse_open_command(self, text):
        """Analisa comandos de 'abrir'"""
        if 'opera' in text:
            return {'action': 'open_application', 'parameters': {'app': 'opera'}}
        elif 'chrome' in text or 'navegador' in text:
            return {'action': 'open_application', 'parameters': {'app': 'chrome'}}
        elif 'notepad' in text or 'bloco de notas' in text:
            return {'action': 'open_application', 'parameters': {'app': 'notepad'}}
        elif 'calculadora' in text:
            return {'action': 'open_application', 'parameters': {'app': 'calculator'}}
        elif 'site' in text or 'página' in text:
            url_match = re.search(r'(https?://\S+|www\.\S+|\S+\.\S+)', text)
            url = url_match.group(1) if url_match else 'google.com'
            return {'action': 'open_website', 'parameters': {'url': url}}
        return None

    def _parse_close_command(self, text):
        """Analisa comandos de 'fechar'"""
        if 'opera' in text:
            return {'action': 'close_application', 'parameters': {'app': 'opera'}}
        elif 'chrome' in text:
            return {'action': 'close_application', 'parameters': {'app': 'chrome'}}
        elif 'aplicativo' in text or 'programa' in text:
            return {'action': 'close_active_window', 'parameters': {}}
        return None

    def _parse_execute_command(self, text):
        return {'action': 'execute_command', 'parameters': {'command': text}}

    def _parse_navigate_command(self, text):
        url_match = re.search(r'(https?://\S+|www\.\S+|\S+\.\S+)', text)
        url = url_match.group(1) if url_match else 'google.com'
        return {'action': 'open_website', 'parameters': {'url': url}}

    def _parse_search_command(self, text):
        search_terms = ['pesquisar', 'procurar', 'buscar']
        for term in search_terms:
            if term in text:
                query = text.split(term, 1)[1].strip()
                return {'action': 'web_search', 'parameters': {'query': query}}
        return None

    def _parse_volume_command(self, text):
        if 'aumentar' in text or 'subir' in text:
            return {'action': 'volume_up', 'parameters': {}}
        elif 'diminuir' in text or 'baixar' in text:
            return {'action': 'volume_down', 'parameters': {}}
        elif 'mudo' in text or 'silenciar' in text:
            return {'action': 'volume_mute', 'parameters': {}}
        return None

    def _parse_music_command(self, text):
        if 'tocar' in text or 'reproduzir' in text:
            return {'action': 'play_music', 'parameters': {}}
        elif 'pausar' in text or 'parar' in text:
            return {'action': 'pause_music', 'parameters': {}}
        return None

    def _parse_video_command(self, text):
        if 'youtube' in text:
            return {'action': 'open_website', 'parameters': {'url': 'youtube.com'}}
        return None

    def _parse_game_command(self, text):
        games = {
            'solitaire': 'solitaire', 'paciência': 'solitaire',
            'minesweeper': 'minesweeper', 'campo minado': 'minesweeper'
        }
        for game_name, game_id in games.items():
            if game_name in text:
                return {'action': 'open_game', 'parameters': {'game': game_id}}
        return None

    def _parse_app_command(self, text):
        return {'action': 'open_application', 'parameters': {'app': 'generic'}}

    def _generate_system_response(self, action, intent):
        """Gera resposta para comandos de sistema"""
        action_type = action['action']

        responses = {
            'open_application': "Perfeito! Vou abrir isso para você agora mesmo!",
            'close_application': "Claro! Fechando o aplicativo para você!",
            'open_website': "Ótimo! Abrindo o site que você pediu!",
            'web_search': "Excelente! Vou pesquisar isso para você!",
            'volume_up': "Aumentando o volume para você!",
            'volume_down': "Diminuindo o volume!",
            'volume_mute': "Silenciando o áudio!",
            'play_music': "Tocando música para você!",
            'pause_music': "Pausando a música!",
            'open_game': "Que divertido! Abrindo o jogo!",
            'execute_command': "Executando comando!",
            'close_active_window': "Fechando a janela ativa!"
        }

        return responses.get(action_type, "Executando sua solicitação!")

    def _add_to_history(self, role, content):
        """Adiciona mensagem ao histórico"""
        self.conversation_history.append({
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        })

        # Manter apenas últimas 50 mensagens
        if len(self.conversation_history) > 50:
            self.conversation_history = self.conversation_history[-50:]

    def get_conversation_history(self):
        """Retorna histórico da conversa"""
        return self.conversation_history.copy()

    def clear_history(self):
        """Limpa histórico da conversa"""
        self.conversation_history.clear()
        self.logger.info("Histórico da conversa limpo")

# Alias para compatibilidade
AIBrain = AdvancedAIBrain
