// SARA AI - JavaScript do Painel de Controle

class SaraPanel {
    constructor() {
        this.socket = null;
        this.isConnected = false;
        this.init();
    }

    init() {
        this.setupSocketConnection();
        this.setupEventListeners();
        this.loadInitialData();
        this.startPeriodicUpdates();
    }

    setupSocketConnection() {
        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('Conectado ao servidor');
            this.isConnected = true;
            this.updateConnectionStatus(true);
        });

        this.socket.on('disconnect', () => {
            console.log('Desconectado do servidor');
            this.isConnected = false;
            this.updateConnectionStatus(false);
        });

        this.socket.on('status_update', (status) => {
            this.updateSystemStatus(status);
        });

        this.socket.on('history_update', (entry) => {
            this.addToConversationHistory(entry);
        });

        this.socket.on('new_command', (data) => {
            this.addToConversationHistory({
                type: 'command',
                content: data.command,
                timestamp: data.timestamp,
                source: 'voice'
            });
        });
    }

    setupEventListeners() {
        // Comando de envio
        const sendButton = document.getElementById('sendCommand');
        const commandInput = document.getElementById('commandInput');

        sendButton.addEventListener('click', () => this.sendCommand());
        commandInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendCommand();
        });

        // Configurações de voz
        const voiceRate = document.getElementById('voiceRate');
        const voiceVolume = document.getElementById('voiceVolume');

        voiceRate.addEventListener('input', (e) => {
            document.getElementById('voiceRateValue').textContent = e.target.value;
            this.updateVoiceConfig('rate', e.target.value);
        });

        voiceVolume.addEventListener('input', (e) => {
            document.getElementById('voiceVolumeValue').textContent = e.target.value + '%';
            this.updateVoiceConfig('volume', e.target.value / 100);
        });

        // Testes de voz
        document.getElementById('testSynthesis').addEventListener('click', () => {
            this.testVoice('synthesis');
        });

        document.getElementById('testRecognition').addEventListener('click', () => {
            this.testVoice('recognition');
        });

        // Limpar histórico
        document.getElementById('clearHistory').addEventListener('click', () => {
            this.clearConversationHistory();
        });

        // Atualizar logs
        document.getElementById('refreshLogs').addEventListener('click', () => {
            this.loadLogs();
        });
    }

    updateConnectionStatus(connected) {
        const statusDot = document.getElementById('statusDot');
        const statusText = document.getElementById('statusText');

        if (connected) {
            statusDot.classList.add('connected');
            statusText.textContent = 'Conectado';
        } else {
            statusDot.classList.remove('connected');
            statusText.textContent = 'Desconectado';
        }
    }

    updateSystemStatus(status) {
        // Atualizar status de escuta
        const listeningStatus = document.getElementById('listeningStatus');
        listeningStatus.textContent = status.is_listening ? 'Sim' : 'Não';
        listeningStatus.style.color = status.is_listening ? '#28a745' : '#dc3545';

        // Atualizar último comando
        if (status.last_command) {
            document.getElementById('lastCommand').textContent = status.last_command;
        }

        // Atualizar última resposta
        if (status.last_response) {
            document.getElementById('lastResponse').textContent = status.last_response;
        }

        // Atualizar status de processamento
        const processingStatus = document.getElementById('processingStatus');
        processingStatus.textContent = status.is_running ? 'Sim' : 'Não';
        processingStatus.style.color = status.is_running ? '#28a745' : '#dc3545';
    }

    async sendCommand() {
        const input = document.getElementById('commandInput');
        const command = input.value.trim();

        if (!command) return;

        try {
            // Adicionar ao histórico imediatamente
            this.addToConversationHistory({
                type: 'user',
                content: command,
                timestamp: new Date().toISOString(),
                source: 'web_panel'
            });

            // Enviar comando para o servidor
            const response = await fetch('/api/send_command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ command })
            });

            const result = await response.json();

            if (result.success) {
                input.value = '';
                this.showNotification('Comando enviado com sucesso', 'success');
            } else {
                this.showNotification('Erro ao enviar comando: ' + result.error, 'error');
            }

        } catch (error) {
            console.error('Erro ao enviar comando:', error);
            this.showNotification('Erro de conexão', 'error');
        }
    }

    sendQuickCommand(command) {
        document.getElementById('commandInput').value = command;
        this.sendCommand();
    }

    addToConversationHistory(entry) {
        const historyContainer = document.getElementById('conversationHistory');
        const messageDiv = document.createElement('div');
        
        let className = 'conversation-item ';
        if (entry.type === 'user' || entry.type === 'command') {
            className += 'user';
        } else if (entry.type === 'assistant' || entry.type === 'response') {
            className += 'assistant';
        } else {
            className += 'system';
        }

        messageDiv.className = className;
        
        const timestamp = new Date(entry.timestamp).toLocaleTimeString();
        const source = entry.source === 'voice' ? '🎤' : '💻';
        
        messageDiv.innerHTML = `
            <div class="message-content">
                <span class="timestamp">${source} ${timestamp}</span>
                <p>${entry.content}</p>
            </div>
        `;

        historyContainer.appendChild(messageDiv);
        historyContainer.scrollTop = historyContainer.scrollHeight;

        // Manter apenas últimas 50 mensagens
        while (historyContainer.children.length > 50) {
            historyContainer.removeChild(historyContainer.firstChild);
        }
    }

    clearConversationHistory() {
        const historyContainer = document.getElementById('conversationHistory');
        historyContainer.innerHTML = `
            <div class="conversation-item system">
                <div class="message-content">
                    <span class="timestamp">Sistema</span>
                    <p>Histórico limpo</p>
                </div>
            </div>
        `;
    }

    async updateVoiceConfig(property, value) {
        try {
            const response = await fetch('/api/config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    voice: {
                        [property]: value
                    }
                })
            });

            const result = await response.json();
            if (!result.success) {
                console.error('Erro ao atualizar configuração:', result.error);
            }

        } catch (error) {
            console.error('Erro ao atualizar configuração:', error);
        }
    }

    async testVoice(type) {
        try {
            const button = document.getElementById(`test${type.charAt(0).toUpperCase() + type.slice(1)}`);
            const originalText = button.innerHTML;
            
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testando...';
            button.disabled = true;

            const response = await fetch('/api/voice_test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    type: type,
                    text: type === 'synthesis' ? 'Este é um teste de síntese de voz da SARA AI' : undefined
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.showNotification(result.message, 'success');
            } else {
                this.showNotification('Erro no teste: ' + result.error, 'error');
            }

        } catch (error) {
            console.error('Erro no teste de voz:', error);
            this.showNotification('Erro de conexão', 'error');
        } finally {
            setTimeout(() => {
                const button = document.getElementById(`test${type.charAt(0).toUpperCase() + type.slice(1)}`);
                button.innerHTML = originalText;
                button.disabled = false;
            }, 2000);
        }
    }

    async loadInitialData() {
        await Promise.all([
            this.loadSystemInfo(),
            this.loadConversationHistory(),
            this.loadConfiguration(),
            this.loadLogs()
        ]);
    }

    async loadSystemInfo() {
        try {
            const response = await fetch('/api/system_info');
            const info = await response.json();

            document.getElementById('cpuUsage').textContent = info.cpu_usage + '%';
            document.getElementById('memoryUsage').textContent = info.memory_usage + '%';

        } catch (error) {
            console.error('Erro ao carregar info do sistema:', error);
        }
    }

    async loadConversationHistory() {
        try {
            const response = await fetch('/api/history');
            const history = await response.json();

            const historyContainer = document.getElementById('conversationHistory');
            historyContainer.innerHTML = '';

            history.forEach(entry => {
                this.addToConversationHistory(entry);
            });

        } catch (error) {
            console.error('Erro ao carregar histórico:', error);
        }
    }

    async loadConfiguration() {
        try {
            const response = await fetch('/api/config');
            const config = await response.json();

            // Atualizar controles de configuração
            if (config.voice_rate) {
                document.getElementById('voiceRate').value = config.voice_rate;
                document.getElementById('voiceRateValue').textContent = config.voice_rate;
            }

            if (config.voice_volume) {
                const volumePercent = Math.round(config.voice_volume * 100);
                document.getElementById('voiceVolume').value = volumePercent;
                document.getElementById('voiceVolumeValue').textContent = volumePercent + '%';
            }

        } catch (error) {
            console.error('Erro ao carregar configuração:', error);
        }
    }

    async loadLogs() {
        try {
            const response = await fetch('/api/logs');
            const logs = await response.json();

            const logsContainer = document.getElementById('systemLogs');
            logsContainer.innerHTML = '';

            logs.forEach(logLine => {
                const logDiv = document.createElement('div');
                logDiv.className = 'log-entry';
                
                // Parse básico do log
                const parts = logLine.split(' - ');
                if (parts.length >= 3) {
                    const time = parts[0].split(' ')[1] || '00:00:00';
                    const level = parts[2] || 'INFO';
                    const message = parts.slice(3).join(' - ') || logLine;

                    logDiv.innerHTML = `
                        <span class="log-time">${time}</span>
                        <span class="log-level ${level}">${level}</span>
                        <span class="log-message">${message}</span>
                    `;
                } else {
                    logDiv.innerHTML = `
                        <span class="log-time">--:--:--</span>
                        <span class="log-level INFO">INFO</span>
                        <span class="log-message">${logLine}</span>
                    `;
                }

                logsContainer.appendChild(logDiv);
            });

            logsContainer.scrollTop = logsContainer.scrollHeight;

        } catch (error) {
            console.error('Erro ao carregar logs:', error);
        }
    }

    startPeriodicUpdates() {
        // Atualizar informações do sistema a cada 5 segundos
        setInterval(() => {
            if (this.isConnected) {
                this.loadSystemInfo();
            }
        }, 5000);

        // Atualizar logs a cada 10 segundos
        setInterval(() => {
            if (this.isConnected) {
                this.loadLogs();
            }
        }, 10000);
    }

    showNotification(message, type = 'info') {
        // Criar elemento de notificação
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;
        
        // Estilos inline para notificação
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            animation: slideIn 0.3s ease;
            max-width: 300px;
        `;

        // Cores baseadas no tipo
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8'
        };

        notification.style.backgroundColor = colors[type] || colors.info;

        document.body.appendChild(notification);

        // Remover após 3 segundos
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
}

// Funções globais para botões rápidos
function sendQuickCommand(command) {
    window.saraPanel.sendQuickCommand(command);
}

// Inicializar quando a página carregar
document.addEventListener('DOMContentLoaded', () => {
    window.saraPanel = new SaraPanel();
});

// Adicionar animações CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
