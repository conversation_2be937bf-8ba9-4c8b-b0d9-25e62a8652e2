"""
SARA AI - Teste dos Sistemas Avançados
Testa os novos componentes individualmente
"""

import sys
import os

def test_voice_synthesis():
    """Testa síntese de voz avançada"""
    print("🧪 Testando síntese de voz avançada...")
    try:
        from src.voice_synthesis_advanced import AdvancedVoiceSynthesis
        
        tts = AdvancedVoiceSynthesis()
        
        # Mostrar engines disponíveis
        engines = tts.get_available_engines()
        print(f"   Engines disponíveis: {list(engines.keys())}")
        print(f"   Engine atual: {tts.current_engine}")
        
        # Testar fala
        print("   Testando fala...")
        tts.test_speech("Olá! Esta é minha nova voz mais natural e feminina!")
        
        print("✅ Síntese de voz avançada funcionando")
        return True
        
    except Exception as e:
        print(f"❌ Erro na síntese avançada: {e}")
        return False

def test_ai_brain():
    """Testa IA avançada"""
    print("🧪 Testando IA avançada...")
    try:
        from src.ai_brain_advanced import AdvancedAIBrain
        
        ai = AdvancedAIBrain()
        
        # Testar processamento
        response = ai.process_command("olá sara")
        print(f"   Resposta: {response['text']}")
        print(f"   Intenção: {response['intent']}")
        print(f"   Confiança: {response['confidence']}")
        
        # Testar estatísticas
        stats = ai.get_learning_stats()
        print(f"   Stats: {stats}")
        
        print("✅ IA avançada funcionando")
        return True
        
    except Exception as e:
        print(f"❌ Erro na IA avançada: {e}")
        return False

def test_training_system():
    """Testa sistema de treinamento"""
    print("🧪 Testando sistema de treinamento...")
    try:
        from src.ai_brain_advanced import AdvancedAIBrain
        from src.training_system import TrainingSystem
        
        ai = AdvancedAIBrain()
        training = TrainingSystem(ai)
        
        # Testar correção
        success, message = training.correct_response(
            "teste", "resposta errada", "resposta correta", "teste de correção"
        )
        print(f"   Correção: {success} - {message}")
        
        # Testar novo comando
        success, message = training.teach_new_command(
            "abrir teste", "open_application", {"app": "teste"}, "comando de teste"
        )
        print(f"   Novo comando: {success} - {message}")
        
        # Testar métricas
        metrics = training.get_performance_metrics()
        print(f"   Métricas: {metrics}")
        
        print("✅ Sistema de treinamento funcionando")
        return True
        
    except Exception as e:
        print(f"❌ Erro no sistema de treinamento: {e}")
        return False

def test_basic_components():
    """Testa componentes básicos"""
    print("🧪 Testando componentes básicos...")
    try:
        from src.voice_recognition import VoiceRecognition
        from src.system_controller import SystemController
        
        # Testar reconhecimento de voz
        vr = VoiceRecognition()
        print("   ✅ Reconhecimento de voz inicializado")
        
        # Testar controlador de sistema
        sc = SystemController()
        info = sc.get_system_info()
        print(f"   ✅ Sistema: CPU {info.get('cpu_percent', 0)}%")
        
        print("✅ Componentes básicos funcionando")
        return True
        
    except Exception as e:
        print(f"❌ Erro nos componentes básicos: {e}")
        return False

def main():
    """Função principal de teste"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                SARA AI - TESTE AVANÇADO                      ║
    ║              Testando Novos Componentes                      ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    tests = [
        ("Componentes Básicos", test_basic_components),
        ("Síntese de Voz Avançada", test_voice_synthesis),
        ("IA Avançada", test_ai_brain),
        ("Sistema de Treinamento", test_training_system)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"💥 Erro inesperado em {test_name}: {e}")
            results.append((test_name, False))
    
    # Resumo
    print("\n" + "="*60)
    print("RESUMO DOS TESTES")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Resultado: {passed}/{len(results)} testes passaram")
    
    if passed == len(results):
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("   Os sistemas avançados estão funcionando!")
    else:
        print("\n⚠️  ALGUNS TESTES FALHARAM")
        print("   Verifique os erros acima")

if __name__ == "__main__":
    try:
        main()
        input("\nPressione Enter para sair...")
    except KeyboardInterrupt:
        print("\n\nTestes cancelados pelo usuário")
    except Exception as e:
        print(f"\nErro inesperado: {e}")
        input("Pressione Enter para sair...")
