"""
SARA AI - Sistema de Logging
Configuração centralizada de logs para monitoramento
"""

import logging
import colorlog
import os
from config import config

def setup_logger(name):
    """Configura e retorna um logger personalizado"""
    
    # Criar diretório de logs se não existir
    os.makedirs(os.path.dirname(config.LOG_FILE), exist_ok=True)
    
    # Configurar logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, config.LOG_LEVEL))
    
    # Evitar duplicação de handlers
    if logger.handlers:
        return logger
    
    # Handler para console com cores
    console_handler = colorlog.StreamHandler()
    console_formatter = colorlog.ColoredFormatter(
        '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S',
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # Handler para arquivo
    file_handler = logging.FileHandler(config.LOG_FILE, encoding='utf-8')
    file_formatter = logging.Formatter(config.LOG_FORMAT)
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)
    
    return logger
