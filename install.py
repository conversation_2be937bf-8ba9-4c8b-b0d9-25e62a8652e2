"""
SARA AI - Script de Instalação
Instala dependências e configura o ambiente
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path

def print_banner():
    """Exibe banner de instalação"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    SARA AI - INSTALAÇÃO                      ║
    ║              Assistente de IA Conversacional                 ║
    ║                                                              ║
    ║  Este script irá instalar todas as dependências necessárias ║
    ║  para executar o SARA AI em seu sistema.                    ║
    ╚══════════════════════════════════════════════════════════════╝
    """)

def check_python_version():
    """Verifica versão do Python"""
    print("🔍 Verificando versão do Python...")
    
    if sys.version_info < (3, 8):
        print("❌ ERRO: Python 3.8 ou superior é necessário!")
        print(f"   Versão atual: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} detectado")
    return True

def check_pip():
    """Verifica se pip está disponível"""
    print("🔍 Verificando pip...")
    
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip está disponível")
        return True
    except subprocess.CalledProcessError:
        print("❌ ERRO: pip não encontrado!")
        return False

def install_dependencies():
    """Instala dependências do requirements.txt com tratamento de erros"""
    print("📦 Instalando dependências...")

    try:
        # Atualizar ferramentas de build primeiro
        print("   Atualizando ferramentas de build...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade",
                       "pip", "setuptools", "wheel"], check=True)

        # Instalar dependências essenciais uma por uma
        essential_packages = [
            "SpeechRecognition>=3.8.1",
            "pyttsx3>=2.90",
            "Flask>=2.3.0",
            "Flask-CORS>=4.0.0",
            "pyautogui>=0.9.50",
            "psutil>=5.8.0",
            "python-dotenv>=0.19.0",
            "requests>=2.28.0",
            "colorlog>=6.6.0"
        ]

        print("   Instalando pacotes essenciais...")
        for package in essential_packages:
            try:
                print(f"     Instalando {package.split('>=')[0]}...")
                subprocess.run([sys.executable, "-m", "pip", "install", package],
                              check=True, capture_output=True)
            except subprocess.CalledProcessError as e:
                print(f"     ⚠️  Falha em {package.split('>=')[0]} - continuando...")

        # Tentar instalar pacotes opcionais
        optional_packages = [
            "nltk>=3.8",
            "textblob>=0.17.1"
        ]

        print("   Instalando pacotes opcionais...")
        for package in optional_packages:
            try:
                print(f"     Instalando {package.split('>=')[0]}...")
                subprocess.run([sys.executable, "-m", "pip", "install", package],
                              check=True, capture_output=True)
            except subprocess.CalledProcessError:
                print(f"     ⚠️  {package.split('>=')[0]} não instalado (opcional)")

        # Tentar instalar PyAudio (problemático no Windows)
        print("   Tentando instalar PyAudio...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyaudio"],
                          check=True, capture_output=True)
            print("     ✅ PyAudio instalado")
        except subprocess.CalledProcessError:
            print("     ⚠️  PyAudio falhou - reconhecimento de voz pode não funcionar")
            print("     💡 Instale manualmente: pip install pipwin && pipwin install pyaudio")

        print("✅ Instalação de dependências concluída!")
        return True

    except subprocess.CalledProcessError as e:
        print(f"❌ ERRO ao instalar dependências: {e}")
        print("\n💡 Dicas para resolver problemas:")
        print("   - Execute como administrador")
        print("   - Verifique sua conexão com a internet")
        print("   - Para PyAudio: pip install pipwin && pipwin install pyaudio")
        print("   - Tente: pip install --user <pacote>")
        return False

def setup_environment():
    """Configura ambiente inicial"""
    print("⚙️  Configurando ambiente...")
    
    try:
        # Criar arquivo .env se não existir
        if not os.path.exists('.env'):
            print("   Criando arquivo .env...")
            shutil.copy('.env.example', '.env')
            print("   ⚠️  IMPORTANTE: Configure sua chave OpenAI no arquivo .env")
        
        # Verificar estrutura de pastas
        required_dirs = ['logs', 'src', 'web', 'web/templates', 'web/static']
        for dir_path in required_dirs:
            if not os.path.exists(dir_path):
                os.makedirs(dir_path, exist_ok=True)
                print(f"   Criado diretório: {dir_path}")
        
        print("✅ Ambiente configurado!")
        return True
        
    except Exception as e:
        print(f"❌ ERRO na configuração: {e}")
        return False

def test_imports():
    """Testa importações principais"""
    print("🧪 Testando importações...")
    
    test_modules = [
        'speech_recognition',
        'pyttsx3',
        'flask',
        'pyautogui',
        'psutil'
    ]
    
    failed_imports = []
    
    for module in test_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module}")
            failed_imports.append(module)
    
    if failed_imports:
        print(f"\n⚠️  Módulos com problema: {', '.join(failed_imports)}")
        print("   Tente reinstalar com: pip install --force-reinstall <módulo>")
        return False
    
    print("✅ Todas as importações funcionando!")
    return True

def create_desktop_shortcut():
    """Cria atalho na área de trabalho (Windows)"""
    if os.name != 'nt':
        return True
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        path = os.path.join(desktop, "SARA AI.lnk")
        target = os.path.join(os.getcwd(), "main.py")
        wDir = os.getcwd()
        icon = target
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{target}"'
        shortcut.WorkingDirectory = wDir
        shortcut.IconLocation = icon
        shortcut.save()
        
        print("✅ Atalho criado na área de trabalho!")
        return True
        
    except ImportError:
        print("⚠️  Não foi possível criar atalho (módulos opcionais não instalados)")
        return True
    except Exception as e:
        print(f"⚠️  Erro ao criar atalho: {e}")
        return True

def show_next_steps():
    """Mostra próximos passos"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                     INSTALAÇÃO CONCLUÍDA!                    ║
    ╚══════════════════════════════════════════════════════════════╝
    
    📋 PRÓXIMOS PASSOS:
    
    1. 🔑 Configure sua chave OpenAI:
       - Abra o arquivo .env
       - Adicione: OPENAI_API_KEY=sua_chave_aqui
       - Obtenha em: https://platform.openai.com/api-keys
    
    2. 🎤 Teste seu microfone:
       - Verifique se está funcionando
       - Configure permissões se necessário
    
    3. 🚀 Execute o SARA AI:
       - Execute: python main.py
       - Ou use o atalho na área de trabalho
    
    4. 🌐 Acesse o painel web:
       - Abra: http://localhost:5000
       - Monitore e controle o sistema
    
    ╔══════════════════════════════════════════════════════════════╗
    ║  Para suporte: https://github.com/seu-usuario/sara-ai        ║
    ╚══════════════════════════════════════════════════════════════╝
    """)

def main():
    """Função principal de instalação"""
    print_banner()
    
    # Verificações iniciais
    if not check_python_version():
        return False
    
    if not check_pip():
        return False
    
    # Instalação
    if not install_dependencies():
        return False
    
    if not setup_environment():
        return False
    
    if not test_imports():
        return False
    
    # Opcionais
    create_desktop_shortcut()
    
    # Finalização
    show_next_steps()
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 Instalação concluída com sucesso!")
            input("\nPressione Enter para continuar...")
        else:
            print("\n💥 Instalação falhou!")
            input("\nPressione Enter para sair...")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Instalação cancelada pelo usuário")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Erro inesperado: {e}")
        input("\nPressione Enter para sair...")
        sys.exit(1)
