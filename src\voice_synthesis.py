"""
SARA AI - Sistema de Síntese de Voz
Converte texto em fala usando pyttsx3
"""

import pyttsx3
import threading
import queue
import time
from config import config
from src.logger import setup_logger

class VoiceSynthesis:
    """Classe para síntese de voz (text-to-speech)"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.engine = None
        self.is_speaking = False
        self.speech_queue = queue.Queue()
        
        # Inicializar engine
        self._setup_engine()
        
        # Iniciar thread de processamento de fala
        self._start_speech_thread()
        
    def _setup_engine(self):
        """Configura o engine de síntese de voz"""
        try:
            self.logger.info("Inicializando engine de síntese de voz...")
            
            self.engine = pyttsx3.init()
            
            # Configurar propriedades
            self._configure_voice()
            
            self.logger.info("Engine de síntese configurado com sucesso")
            
        except Exception as e:
            self.logger.error(f"Erro ao configurar engine de síntese: {e}")
            raise
    
    def _configure_voice(self):
        """Configura as propriedades da voz"""
        try:
            # Configurar velocidade
            self.engine.setProperty('rate', config.VOICE_RATE)
            
            # Configurar volume
            self.engine.setProperty('volume', config.VOICE_VOLUME)
            
            # Tentar configurar voz em português
            voices = self.engine.getProperty('voices')
            
            # Procurar por voz em português
            portuguese_voice = None
            for voice in voices:
                if 'portuguese' in voice.name.lower() or 'brasil' in voice.name.lower():
                    portuguese_voice = voice
                    break
                elif 'pt' in voice.id.lower():
                    portuguese_voice = voice
                    break
            
            if portuguese_voice:
                self.engine.setProperty('voice', portuguese_voice.id)
                self.logger.info(f"Voz configurada: {portuguese_voice.name}")
            else:
                self.logger.warning("Voz em português não encontrada, usando voz padrão")
                
        except Exception as e:
            self.logger.error(f"Erro ao configurar propriedades da voz: {e}")
    
    def _start_speech_thread(self):
        """Inicia thread para processamento de fala"""
        def speech_worker():
            while True:
                try:
                    # Pegar próximo texto da fila
                    text = self.speech_queue.get(timeout=1)
                    
                    if text is None:  # Sinal para parar
                        break
                        
                    # Falar o texto
                    self._speak_now(text)
                    
                    # Marcar tarefa como concluída
                    self.speech_queue.task_done()
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    self.logger.error(f"Erro na thread de fala: {e}")
        
        # Iniciar thread
        self.speech_thread = threading.Thread(target=speech_worker, daemon=True)
        self.speech_thread.start()
    
    def _speak_now(self, text):
        """Fala o texto imediatamente (uso interno)"""
        try:
            self.is_speaking = True
            self.logger.debug(f"Falando: {text}")
            
            self.engine.say(text)
            self.engine.runAndWait()
            
            self.is_speaking = False
            
        except Exception as e:
            self.logger.error(f"Erro ao falar: {e}")
            self.is_speaking = False
    
    def speak(self, text, priority=False):
        """
        Adiciona texto à fila de fala
        
        Args:
            text: Texto para falar
            priority: Se True, adiciona no início da fila
        """
        try:
            if not text or not text.strip():
                return
                
            self.logger.info(f"Adicionando à fila de fala: {text}")
            
            if priority:
                # Criar nova fila com prioridade
                temp_queue = queue.Queue()
                temp_queue.put(text)
                
                # Mover itens da fila atual para temporária
                while not self.speech_queue.empty():
                    try:
                        item = self.speech_queue.get_nowait()
                        temp_queue.put(item)
                    except queue.Empty:
                        break
                
                # Substituir fila
                self.speech_queue = temp_queue
            else:
                self.speech_queue.put(text)
                
        except Exception as e:
            self.logger.error(f"Erro ao adicionar texto à fila: {e}")
    
    def speak_immediately(self, text):
        """
        Fala o texto imediatamente, interrompendo fala atual
        
        Args:
            text: Texto para falar
        """
        try:
            # Parar fala atual
            self.stop_speaking()
            
            # Limpar fila
            self.clear_queue()
            
            # Falar imediatamente
            self._speak_now(text)
            
        except Exception as e:
            self.logger.error(f"Erro ao falar imediatamente: {e}")
    
    def stop_speaking(self):
        """Para a fala atual"""
        try:
            if self.is_speaking:
                self.engine.stop()
                self.is_speaking = False
                self.logger.info("Fala interrompida")
                
        except Exception as e:
            self.logger.error(f"Erro ao parar fala: {e}")
    
    def clear_queue(self):
        """Limpa a fila de fala"""
        try:
            while not self.speech_queue.empty():
                try:
                    self.speech_queue.get_nowait()
                    self.speech_queue.task_done()
                except queue.Empty:
                    break
                    
            self.logger.info("Fila de fala limpa")
            
        except Exception as e:
            self.logger.error(f"Erro ao limpar fila: {e}")
    
    def is_queue_empty(self):
        """Verifica se a fila de fala está vazia"""
        return self.speech_queue.empty()
    
    def wait_until_done(self, timeout=None):
        """
        Aguarda até que toda a fala seja concluída
        
        Args:
            timeout: Tempo limite em segundos
        """
        try:
            if timeout:
                start_time = time.time()
                while not self.speech_queue.empty() or self.is_speaking:
                    if time.time() - start_time > timeout:
                        self.logger.warning("Timeout aguardando conclusão da fala")
                        break
                    time.sleep(0.1)
            else:
                self.speech_queue.join()
                while self.is_speaking:
                    time.sleep(0.1)
                    
        except Exception as e:
            self.logger.error(f"Erro ao aguardar conclusão da fala: {e}")
    
    def get_available_voices(self):
        """Retorna lista de vozes disponíveis"""
        try:
            voices = self.engine.getProperty('voices')
            voice_list = []
            
            for voice in voices:
                voice_info = {
                    'id': voice.id,
                    'name': voice.name,
                    'languages': getattr(voice, 'languages', []),
                    'gender': getattr(voice, 'gender', 'unknown')
                }
                voice_list.append(voice_info)
                
            return voice_list
            
        except Exception as e:
            self.logger.error(f"Erro ao obter vozes disponíveis: {e}")
            return []
    
    def set_voice(self, voice_id):
        """
        Define a voz a ser usada
        
        Args:
            voice_id: ID da voz
        """
        try:
            self.engine.setProperty('voice', voice_id)
            self.logger.info(f"Voz alterada para: {voice_id}")
            
        except Exception as e:
            self.logger.error(f"Erro ao alterar voz: {e}")
    
    def set_rate(self, rate):
        """
        Define a velocidade da fala
        
        Args:
            rate: Velocidade (palavras por minuto)
        """
        try:
            self.engine.setProperty('rate', rate)
            self.logger.info(f"Velocidade alterada para: {rate}")
            
        except Exception as e:
            self.logger.error(f"Erro ao alterar velocidade: {e}")
    
    def set_volume(self, volume):
        """
        Define o volume da fala
        
        Args:
            volume: Volume (0.0 a 1.0)
        """
        try:
            volume = max(0.0, min(1.0, volume))  # Garantir range válido
            self.engine.setProperty('volume', volume)
            self.logger.info(f"Volume alterado para: {volume}")
            
        except Exception as e:
            self.logger.error(f"Erro ao alterar volume: {e}")
    
    def test_speech(self, text="Olá, este é um teste de síntese de voz."):
        """
        Testa a síntese de voz
        
        Args:
            text: Texto para teste
        """
        try:
            self.logger.info("Testando síntese de voz...")
            self.speak_immediately(text)
            return True
            
        except Exception as e:
            self.logger.error(f"Erro no teste de síntese: {e}")
            return False
