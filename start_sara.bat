@echo off
title SARA AI - Assistente de IA Conversacional

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    SARA AI - INICIANDO                       ║
echo ║              Assistente de IA Conversacional                 ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM Verificar se Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERRO: Python não encontrado!
    echo    Instale Python 3.8+ em: https://python.org
    pause
    exit /b 1
)

REM Verificar se arquivo principal existe
if not exist "main.py" (
    echo ❌ ERRO: main.py não encontrado!
    echo    Execute este arquivo na pasta do SARA AI
    pause
    exit /b 1
)

REM Verificar se dependências estão instaladas
echo 🔍 Verificando dependências...
python -c "import speech_recognition, pyttsx3, flask" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Dependências não instaladas. Executando instalação...
    python install.py
    if errorlevel 1 (
        echo ❌ Falha na instalação!
        pause
        exit /b 1
    )
)

REM Verificar arquivo .env
if not exist ".env" (
    echo ⚠️  Arquivo .env não encontrado!
    echo    Copiando .env.example para .env...
    copy ".env.example" ".env" >nul
    echo.
    echo 🔑 IMPORTANTE: Configure sua chave OpenAI no arquivo .env
    echo    OPENAI_API_KEY=sua_chave_aqui
    echo.
    echo    Pressione qualquer tecla para continuar...
    pause >nul
)

echo.
echo 🚀 Iniciando SARA AI...
echo.
echo 📋 Comandos disponíveis:
echo    - "Hey Sara, abrir Opera"
echo    - "Sara, pesquisar clima"
echo    - "Abrir calculadora"
echo    - "Volume up"
echo.
echo 🌐 Painel Web: http://localhost:5000
echo.
echo ⚠️  Para parar: Ctrl+C ou diga "Sara, parar"
echo.

REM Iniciar SARA AI
python main.py

echo.
echo 👋 SARA AI finalizada!
pause
