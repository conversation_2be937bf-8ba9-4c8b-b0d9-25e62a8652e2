"""
SARA AI - Instalador Simples (Sem PyAudio)
Instala apenas dependências essenciais para funcionamento básico
"""

import subprocess
import sys
import os

def print_banner():
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                SARA AI - INSTALAÇÃO SIMPLES                  ║
    ║              (Sem reconhecimento de voz)                     ║
    ╚══════════════════════════════════════════════════════════════╝
    """)

def install_essential_only():
    """Instala apenas dependências essenciais"""
    print("📦 Instalando dependências essenciais...")
    
    # Pacotes que funcionam em qualquer sistema
    essential_packages = [
        "setuptools>=65.0.0",
        "wheel>=0.37.0",
        "Flask>=2.3.0",
        "Flask-CORS>=4.0.0",
        "Flask-SocketIO>=5.0.0",
        "python-socketio>=5.0.0",
        "pyautogui>=0.9.50",
        "psutil>=5.8.0",
        "python-dotenv>=0.19.0",
        "requests>=2.28.0",
        "colorlog>=6.6.0",
        "pyttsx3>=2.90"  # Síntese de voz funciona sem PyAudio
    ]
    
    try:
        # Atualizar pip
        print("   Atualizando pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True)
        
        # Instalar pacotes essenciais
        for package in essential_packages:
            try:
                print(f"   Instalando {package.split('>=')[0]}...")
                subprocess.run([sys.executable, "-m", "pip", "install", package], 
                              check=True, capture_output=True)
                print(f"   ✅ {package.split('>=')[0]} instalado")
            except subprocess.CalledProcessError as e:
                print(f"   ❌ Erro em {package.split('>=')[0]}: {e}")
                return False
        
        print("✅ Dependências essenciais instaladas!")
        return True
        
    except Exception as e:
        print(f"❌ Erro na instalação: {e}")
        return False

def create_no_voice_config():
    """Cria configuração sem reconhecimento de voz"""
    try:
        # Criar arquivo de configuração especial
        config_content = '''
# SARA AI - Configuração sem reconhecimento de voz
VOICE_RECOGNITION_ENABLED = False
VOICE_SYNTHESIS_ENABLED = True
WEB_PANEL_ONLY = True
'''
        
        with open('config_no_voice.py', 'w', encoding='utf-8') as f:
            f.write(config_content)
            
        print("✅ Configuração sem voz criada!")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar configuração: {e}")
        return False

def create_simple_launcher():
    """Cria launcher simplificado"""
    launcher_content = '''"""
SARA AI - Launcher Simples (Apenas Painel Web)
"""

from web.app import create_app
from config import config

def main():
    print("🌐 SARA AI - Modo Painel Web")
    print(f"   Acesse: http://localhost:{config.FLASK_PORT}")
    print("   Pressione Ctrl+C para parar")
    
    app = create_app()
    app.run(
        host=config.FLASK_HOST,
        port=config.FLASK_PORT,
        debug=config.DEBUG
    )

if __name__ == "__main__":
    main()
'''
    
    try:
        with open('run_web_only.py', 'w', encoding='utf-8') as f:
            f.write(launcher_content)
            
        print("✅ Launcher web criado!")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao criar launcher: {e}")
        return False

def main():
    print_banner()
    
    print("⚠️  AVISO: Esta instalação não inclui reconhecimento de voz")
    print("   Você poderá usar apenas:")
    print("   • Painel web para controle")
    print("   • Síntese de voz para respostas")
    print("   • Comandos via interface web")
    print()
    
    response = input("Continuar? (s/n): ").lower().strip()
    if response != 's':
        print("Instalação cancelada.")
        return
    
    # Instalar dependências
    if not install_essential_only():
        print("💥 Falha na instalação!")
        return
    
    # Configurar ambiente
    if not os.path.exists('.env'):
        try:
            with open('.env.example', 'r') as src:
                content = src.read()
            with open('.env', 'w') as dst:
                dst.write(content)
            print("✅ Arquivo .env criado!")
        except:
            print("⚠️  Crie manualmente o arquivo .env")
    
    # Criar configurações especiais
    create_no_voice_config()
    create_simple_launcher()
    
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                 INSTALAÇÃO SIMPLES CONCLUÍDA!                ║
    ╚══════════════════════════════════════════════════════════════╝
    
    🚀 COMO USAR:
    
    1. Painel Web Apenas:
       python run_web_only.py
       Acesse: http://localhost:5000
    
    2. Sistema Completo (se quiser tentar):
       python main.py
       (Pode não funcionar sem PyAudio)
    
    💡 PARA RECONHECIMENTO DE VOZ:
       Instale PyAudio manualmente:
       pip install pipwin
       pipwin install pyaudio
       
       Ou baixe wheel em: https://www.lfd.uci.edu/~gohlke/pythonlibs/
    
    ╔══════════════════════════════════════════════════════════════╗
    ║  O painel web permite controle completo do sistema!          ║
    ╚══════════════════════════════════════════════════════════════╝
    """)

if __name__ == "__main__":
    try:
        main()
        input("\nPressione Enter para sair...")
    except KeyboardInterrupt:
        print("\n\nInstalação cancelada.")
    except Exception as e:
        print(f"\nErro inesperado: {e}")
        input("Pressione Enter para sair...")
