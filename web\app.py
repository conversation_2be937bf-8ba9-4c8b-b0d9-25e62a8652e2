"""
SARA AI - Aplicação Web Flask
API backend para painel de controle e monitoramento
"""

from flask import Flask, render_template, request, jsonify, session
from flask_cors import CORS
from flask_socketio import SocketIO, emit
import json
import os
from datetime import datetime
from config import config
from src.logger import setup_logger

# Variáveis globais para comunicação com sistema principal
sara_instance = None
conversation_history = []
system_status = {
    'is_running': False,
    'is_listening': False,
    'last_command': None,
    'last_response': None
}

def create_app():
    """Cria e configura a aplicação Flask"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = config.FLASK_SECRET_KEY
    
    # Configurar CORS
    CORS(app)
    
    # Configurar SocketIO
    socketio = SocketIO(app, cors_allowed_origins="*")
    
    # Logger
    logger = setup_logger(__name__)
    
    @app.route('/')
    def index():
        """Página principal do painel"""
        return render_template('index.html')
    
    @app.route('/api/status')
    def get_status():
        """Retorna status atual do sistema"""
        return jsonify(system_status)
    
    @app.route('/api/history')
    def get_history():
        """Retorna histórico de conversas"""
        return jsonify(conversation_history)
    
    @app.route('/api/send_command', methods=['POST'])
    def send_command():
        """Envia comando para o sistema SARA"""
        try:
            data = request.get_json()
            command = data.get('command', '')

            if not command:
                return jsonify({'success': False, 'error': 'Comando vazio'})

            logger.info(f"Comando recebido do painel web: {command}")

            # Processar comando usando a instância SARA
            if sara_instance and hasattr(sara_instance, 'ai_brain'):
                # Processar comando com IA
                ai_response = sara_instance.ai_brain.process_command(command)

                # Executar ação se necessário
                if ai_response.get('action'):
                    action_result = sara_instance.system_controller.execute_action(
                        ai_response['action'],
                        ai_response.get('parameters', {})
                    )

                    if action_result['success']:
                        logger.info(f"Ação executada: {ai_response['action']}")
                    else:
                        logger.error(f"Erro na ação: {action_result['error']}")
                        ai_response['text'] = f"Desculpe, não consegui executar essa ação: {action_result['error']}"

                # Responder por voz se disponível
                if ai_response.get('text') and hasattr(sara_instance, 'voice_synthesis'):
                    sara_instance.voice_synthesis.speak(ai_response['text'])

                response_text = ai_response.get('text', 'Comando processado')

            else:
                # Fallback se não houver instância SARA
                response_text = f"Comando '{command}' recebido, mas sistema principal não está conectado."

            # Adicionar ao histórico
            conversation_history.append({
                'type': 'user',
                'content': command,
                'timestamp': datetime.now().isoformat(),
                'source': 'web_panel'
            })

            conversation_history.append({
                'type': 'assistant',
                'content': response_text,
                'timestamp': datetime.now().isoformat(),
                'source': 'web_panel'
            })

            # Emitir via WebSocket
            socketio.emit('new_command', {
                'command': command,
                'response': response_text,
                'timestamp': datetime.now().isoformat()
            })

            response = {
                'success': True,
                'message': response_text,
                'timestamp': datetime.now().isoformat()
            }

            return jsonify(response)

        except Exception as e:
            logger.error(f"Erro ao processar comando: {e}")
            return jsonify({'success': False, 'error': str(e)})
    
    @app.route('/api/config', methods=['GET', 'POST'])
    def handle_config():
        """Gerencia configurações do sistema"""
        if request.method == 'GET':
            # Retornar configurações atuais (sem dados sensíveis)
            safe_config = {
                'app_name': config.APP_NAME,
                'version': config.VERSION,
                'voice_language': config.VOICE_RECOGNITION_LANGUAGE,
                'voice_rate': config.VOICE_RATE,
                'voice_volume': config.VOICE_VOLUME,
                'wake_words': config.AI_WAKE_WORDS,
                'allowed_commands': config.ALLOWED_COMMANDS
            }
            return jsonify(safe_config)
        
        elif request.method == 'POST':
            try:
                data = request.get_json()
                
                # Atualizar configurações (implementação futura)
                # Por enquanto, apenas simular
                
                return jsonify({
                    'success': True,
                    'message': 'Configurações atualizadas'
                })
                
            except Exception as e:
                logger.error(f"Erro ao atualizar configurações: {e}")
                return jsonify({'success': False, 'error': str(e)})
    
    @app.route('/api/system_info')
    def get_system_info():
        """Retorna informações do sistema"""
        try:
            # Simular informações do sistema
            info = {
                'cpu_usage': 25.5,
                'memory_usage': 60.2,
                'disk_usage': 45.8,
                'uptime': '2h 30m',
                'processes_count': 156,
                'network_status': 'connected'
            }
            
            return jsonify(info)
            
        except Exception as e:
            logger.error(f"Erro ao obter info do sistema: {e}")
            return jsonify({'error': str(e)})
    
    @app.route('/api/logs')
    def get_logs():
        """Retorna logs do sistema"""
        try:
            logs = []
            
            # Ler arquivo de log se existir
            if os.path.exists(config.LOG_FILE):
                with open(config.LOG_FILE, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    # Pegar últimas 100 linhas
                    for line in lines[-100:]:
                        if line.strip():
                            logs.append(line.strip())
            
            return jsonify(logs)
            
        except Exception as e:
            logger.error(f"Erro ao obter logs: {e}")
            return jsonify({'error': str(e)})
    
    @app.route('/api/voice_test', methods=['POST'])
    def voice_test():
        """Testa sistema de voz"""
        try:
            data = request.get_json()
            test_type = data.get('type', 'synthesis')
            
            if test_type == 'synthesis':
                # Testar síntese de voz
                text = data.get('text', 'Este é um teste de síntese de voz.')
                # Implementação futura: integrar com VoiceSynthesis
                
                return jsonify({
                    'success': True,
                    'message': 'Teste de síntese iniciado'
                })
                
            elif test_type == 'recognition':
                # Testar reconhecimento de voz
                # Implementação futura: integrar com VoiceRecognition
                
                return jsonify({
                    'success': True,
                    'message': 'Teste de reconhecimento iniciado'
                })
            
            else:
                return jsonify({
                    'success': False,
                    'error': 'Tipo de teste inválido'
                })
                
        except Exception as e:
            logger.error(f"Erro no teste de voz: {e}")
            return jsonify({'success': False, 'error': str(e)})
    
    # WebSocket events
    @socketio.on('connect')
    def handle_connect():
        """Cliente conectado via WebSocket"""
        logger.info('Cliente conectado via WebSocket')
        emit('status', system_status)
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """Cliente desconectado via WebSocket"""
        logger.info('Cliente desconectado via WebSocket')
    
    @socketio.on('request_status')
    def handle_status_request():
        """Cliente solicitou status"""
        emit('status', system_status)
    
    # Função para atualizar status (chamada pelo sistema principal)
    def update_status(new_status):
        """Atualiza status e notifica clientes"""
        global system_status
        system_status.update(new_status)
        socketio.emit('status_update', system_status)
    
    # Função para adicionar ao histórico (chamada pelo sistema principal)
    def add_to_history(entry):
        """Adiciona entrada ao histórico e notifica clientes"""
        conversation_history.append(entry)
        socketio.emit('history_update', entry)
        
        # Manter apenas últimas 1000 entradas
        if len(conversation_history) > 1000:
            conversation_history[:] = conversation_history[-1000:]
    
    # Anexar funções ao app para acesso externo
    app.update_status = update_status
    app.add_to_history = add_to_history
    app.socketio = socketio
    
    return app

def set_sara_instance(instance):
    """Define instância do SARA para comunicação"""
    global sara_instance
    sara_instance = instance
