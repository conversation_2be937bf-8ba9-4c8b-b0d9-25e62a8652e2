"""
SARA AI - Sistema de Síntese de Voz Avançado
Múltiplas opções de TTS para voz mais natural e feminina
"""

import pyttsx3
import threading
import queue
import time
import os
import asyncio
from config import config
from src.logger import setup_logger

# Tentar importar engines avançados
try:
    import edge_tts
    EDGE_TTS_AVAILABLE = True
except ImportError:
    EDGE_TTS_AVAILABLE = False

try:
    from gtts import gTTS
    import pygame
    GTTS_AVAILABLE = True
except ImportError:
    GTTS_AVAILABLE = False

class AdvancedVoiceSynthesis:
    """Classe para síntese de voz avançada com múltiplas opções"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.is_speaking = False
        self.speech_queue = queue.Queue()
        
        # Configurar engines disponíveis
        self.engines = {}
        self._setup_engines()
        
        # Selecionar melhor engine
        self.current_engine = self._select_best_engine()
        
        # Iniciar thread de processamento
        self._start_speech_thread()
        
    def _setup_engines(self):
        """Configura todos os engines de TTS disponíveis"""
        
        # 1. pyttsx3 (padrão, sempre disponível)
        try:
            pyttsx3_engine = pyttsx3.init()
            self._configure_pyttsx3(pyttsx3_engine)
            self.engines['pyttsx3'] = {
                'engine': pyttsx3_engine,
                'quality': 6,
                'speed': 'fast',
                'available': True
            }
            self.logger.info("Engine pyttsx3 configurado")
        except Exception as e:
            self.logger.error(f"Erro ao configurar pyttsx3: {e}")
        
        # 2. Edge TTS (Microsoft, alta qualidade)
        if EDGE_TTS_AVAILABLE:
            self.engines['edge_tts'] = {
                'engine': None,  # Será usado de forma assíncrona
                'quality': 10,
                'speed': 'medium',
                'available': True,
                'voice': 'pt-BR-FranciscaNeural'  # Voz feminina brasileira
            }
            self.logger.info("Engine Edge TTS disponível")
        
        # 3. Google TTS (boa qualidade, requer internet)
        if GTTS_AVAILABLE:
            self.engines['gtts'] = {
                'engine': None,
                'quality': 8,
                'speed': 'medium',
                'available': True,
                'lang': 'pt-br'
            }
            self.logger.info("Engine Google TTS disponível")
    
    def _configure_pyttsx3(self, engine):
        """Configura pyttsx3 para voz mais feminina e natural"""
        try:
            voices = engine.getProperty('voices')
            
            # Procurar pela melhor voz feminina
            best_voice = None
            female_voices = []
            
            for voice in voices:
                voice_name = voice.name.lower()
                
                # Priorizar vozes femininas em português
                if any(indicator in voice_name for indicator in 
                       ['maria', 'helena', 'francisca', 'female', 'woman']):
                    if any(pt in voice_name for pt in ['portuguese', 'brasil', 'brazil']):
                        best_voice = voice
                        break
                    female_voices.append(voice)
            
            # Se não encontrou voz feminina em português, usar qualquer feminina
            if not best_voice and female_voices:
                best_voice = female_voices[0]
            
            # Aplicar configurações
            if best_voice:
                engine.setProperty('voice', best_voice.id)
                self.logger.info(f"Voz pyttsx3 configurada: {best_voice.name}")
            
            # Configurações para voz mais natural
            engine.setProperty('rate', config.VOICE_RATE - 30)  # Mais lenta
            engine.setProperty('volume', config.VOICE_VOLUME)
            
        except Exception as e:
            self.logger.error(f"Erro ao configurar voz pyttsx3: {e}")
    
    def _select_best_engine(self):
        """Seleciona o melhor engine disponível"""
        # Ordenar por qualidade
        available_engines = [(name, info) for name, info in self.engines.items() 
                           if info['available']]
        
        if not available_engines:
            self.logger.error("Nenhum engine de TTS disponível!")
            return None
        
        # Selecionar o de maior qualidade
        best_engine = max(available_engines, key=lambda x: x[1]['quality'])
        self.logger.info(f"Engine selecionado: {best_engine[0]} (qualidade: {best_engine[1]['quality']})")
        
        return best_engine[0]
    
    def _start_speech_thread(self):
        """Inicia thread para processamento de fala"""
        def speech_worker():
            while True:
                try:
                    text = self.speech_queue.get(timeout=1)
                    if text is None:
                        break
                    
                    self._speak_now(text)
                    self.speech_queue.task_done()
                    
                except queue.Empty:
                    continue
                except Exception as e:
                    self.logger.error(f"Erro na thread de fala: {e}")
        
        self.speech_thread = threading.Thread(target=speech_worker, daemon=True)
        self.speech_thread.start()
    
    def _speak_now(self, text):
        """Fala o texto usando o engine selecionado"""
        try:
            self.is_speaking = True
            self.logger.info(f"INICIANDO FALA ({self.current_engine}): {text}")

            success = False

            if self.current_engine == 'edge_tts':
                success = self._speak_edge_tts(text)
            elif self.current_engine == 'gtts':
                success = self._speak_gtts(text)
            else:  # pyttsx3 (fallback)
                success = self._speak_pyttsx3(text)

            if success:
                self.logger.info(f"FALA CONCLUÍDA COM SUCESSO: {text}")
            else:
                self.logger.warning(f"Falha na fala com {self.current_engine}, tentando fallback...")
                # Fallback para pyttsx3
                if self.current_engine != 'pyttsx3':
                    self.logger.info("Usando fallback pyttsx3...")
                    self._speak_pyttsx3(text)

        except Exception as e:
            self.logger.error(f"ERRO CRÍTICO ao falar com {self.current_engine}: {e}")
            # Fallback para pyttsx3
            if self.current_engine != 'pyttsx3':
                self.logger.info("Tentando fallback para pyttsx3...")
                self._speak_pyttsx3(text)
        finally:
            self.is_speaking = False
            self.logger.info("Status de fala resetado")
    
    def _speak_pyttsx3(self, text):
        """Fala usando pyttsx3"""
        try:
            self.logger.info(f"Usando pyttsx3 para falar: {text}")
            engine = self.engines['pyttsx3']['engine']
            engine.say(text)
            engine.runAndWait()
            self.logger.info("pyttsx3 concluído com sucesso")
            return True
        except Exception as e:
            self.logger.error(f"ERRO no pyttsx3: {e}")
            return False
    
    def _speak_edge_tts(self, text):
        """Fala usando Edge TTS (Microsoft)"""
        try:
            async def _edge_speak():
                voice = self.engines['edge_tts']['voice']
                communicate = edge_tts.Communicate(text, voice)
                
                # Salvar em arquivo temporário
                import tempfile
                temp_file = os.path.join(tempfile.gettempdir(), f"sara_speech_{int(time.time())}.mp3")
                await communicate.save(temp_file)
                
                # Reproduzir arquivo
                if os.path.exists(temp_file):
                    if GTTS_AVAILABLE:  # Usar pygame se disponível
                        pygame.mixer.init()
                        pygame.mixer.music.load(temp_file)
                        pygame.mixer.music.play()
                        while pygame.mixer.music.get_busy():
                            time.sleep(0.1)
                    else:
                        # Fallback: usar player do sistema
                        os.system(f'start "" "{temp_file}"')
                        time.sleep(len(text) * 0.1)  # Estimativa de tempo
                    
                    # Limpar arquivo temporário
                    try:
                        os.remove(temp_file)
                    except:
                        pass
            
            # Executar de forma síncrona
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(_edge_speak())
            loop.close()
            
        except Exception as e:
            self.logger.error(f"Erro no Edge TTS: {e}")
            raise
    
    def _speak_gtts(self, text):
        """Fala usando Google TTS"""
        try:
            tts = gTTS(text=text, lang=self.engines['gtts']['lang'], slow=False)
            import tempfile
            temp_file = os.path.join(tempfile.gettempdir(), f"sara_gtts_{int(time.time())}.mp3")
            tts.save(temp_file)
            
            # Reproduzir
            pygame.mixer.init()
            pygame.mixer.music.load(temp_file)
            pygame.mixer.music.play()
            while pygame.mixer.music.get_busy():
                time.sleep(0.1)
            
            # Limpar
            try:
                os.remove(temp_file)
            except:
                pass
                
        except Exception as e:
            self.logger.error(f"Erro no Google TTS: {e}")
            raise
    
    def speak(self, text, priority=False):
        """Adiciona texto à fila de fala"""
        if not text or not text.strip():
            self.logger.warning("Tentativa de falar texto vazio")
            return

        self.logger.info(f"Adicionando à fila de fala: {text}")

        if priority:
            # Limpar fila e falar imediatamente
            self.clear_queue()
            self.speech_queue.put(text)
            self.logger.info("Texto adicionado com prioridade")
        else:
            self.speech_queue.put(text)
            self.logger.info(f"Texto adicionado à fila (tamanho: {self.speech_queue.qsize()})")
    
    def speak_immediately(self, text):
        """Fala imediatamente, interrompendo fala atual"""
        self.stop_speaking()
        self.clear_queue()
        self._speak_now(text)
    
    def stop_speaking(self):
        """Para a fala atual"""
        self.is_speaking = False
        if 'pyttsx3' in self.engines:
            try:
                self.engines['pyttsx3']['engine'].stop()
            except:
                pass
    
    def clear_queue(self):
        """Limpa a fila de fala"""
        while not self.speech_queue.empty():
            try:
                self.speech_queue.get_nowait()
                self.speech_queue.task_done()
            except queue.Empty:
                break
    
    def change_engine(self, engine_name):
        """Muda o engine de TTS"""
        if engine_name in self.engines and self.engines[engine_name]['available']:
            self.current_engine = engine_name
            self.logger.info(f"Engine alterado para: {engine_name}")
            return True
        return False
    
    def get_available_engines(self):
        """Retorna engines disponíveis"""
        return {name: info for name, info in self.engines.items() if info['available']}
    
    def test_speech(self, text="Olá! Este é um teste da minha nova voz mais natural e feminina!"):
        """Testa a síntese de voz"""
        self.speak_immediately(text)

# Alias para compatibilidade
VoiceSynthesis = AdvancedVoiceSynthesis
