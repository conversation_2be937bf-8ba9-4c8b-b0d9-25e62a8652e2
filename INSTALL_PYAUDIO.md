# 🎤 Como Instalar PyAudio para Reconhecimento de Voz

O PyAudio é necessário apenas para o **reconhecimento de voz**. O SARA AI funciona perfeitamente sem ele, usando apenas o painel web para controle.

## ⚠️ Importante
- **O sistema funciona sem PyAudio** - você pode usar o painel web
- PyAudio é necessário apenas para comandos de voz
- Se não conseguir instalar, use `python install_simple.py`

## 🪟 Windows

### Método 1: Pipwin (Recomendado)
```bash
pip install pipwin
pipwin install pyaudio
```

### Método 2: Wheel Pré-compilado
1. Vá para: https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio
2. Baixe o arquivo `.whl` para sua versão do Python
3. Instale: `pip install nome_do_arquivo.whl`

### Método 3: Microsoft Visual C++
1. Instale o Microsoft Visual C++ Build Tools
2. Execute: `pip install pyaudio`

## 🐧 Linux (Ubuntu/Debian)
```bash
sudo apt-get install portaudio19-dev python3-pyaudio
pip install pyaudio
```

## 🍎 macOS
```bash
brew install portaudio
pip install pyaudio
```

## 🔧 Solução de Problemas

### Erro: "Microsoft Visual C++ 14.0 is required"
- Instale o Visual Studio Build Tools
- Ou use o Método 1 (pipwin)

### Erro: "Failed building wheel for pyaudio"
- Use wheel pré-compilado (Método 2)
- Ou use `python install_simple.py`

### Erro: "No module named '_portaudio'"
- Reinstale usando pipwin
- Ou baixe wheel específico para sua versão

## ✅ Verificar Instalação
```python
import pyaudio
print("PyAudio instalado com sucesso!")
```

## 🌐 Alternativa: Usar Apenas Painel Web
Se não conseguir instalar PyAudio:

1. Execute: `python install_simple.py`
2. Use: `python run_web_only.py`
3. Acesse: http://localhost:5000
4. Controle tudo pelo painel web!

## 📞 Suporte
- O sistema funciona **perfeitamente** sem PyAudio
- Use o painel web para controle completo
- PyAudio é apenas um extra para comandos de voz
