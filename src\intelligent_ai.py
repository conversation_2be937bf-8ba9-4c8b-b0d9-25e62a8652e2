"""
SARA AI - Sistema de IA Verdadeiramente Inteligente
IA que realmente aprende e entende comandos naturais
"""

import json
import sqlite3
import os
from datetime import datetime
from src.nlp_engine import AdvancedNLPEngine
from src.logger import setup_logger

class IntelligentAI:
    """IA que realmente aprende e entende linguagem natural"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        
        # Motor de NLP avançado
        self.nlp_engine = AdvancedNLPEngine()
        
        # Base de dados para aprendizado
        self.db_path = "data/intelligent_ai.db"
        self._setup_database()
        
        # Contexto da conversa
        self.conversation_context = []
        self.learning_mode = False
        self.pending_clarification = None
        
        self.logger.info("IA Inteligente inicializada")
    
    def _setup_database(self):
        """Configura base de dados da IA"""
        try:
            os.makedirs("data", exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Tabela de aprendizado de comandos
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS learned_commands (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trigger_phrase TEXT NOT NULL,
                    action_type TEXT NOT NULL,
                    parameters TEXT,
                    description TEXT,
                    success_count INTEGER DEFAULT 0,
                    failure_count INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    last_used DATETIME
                )
            ''')
            
            # Tabela de feedback do usuário
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_feedback (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_input TEXT NOT NULL,
                    ai_response TEXT NOT NULL,
                    user_correction TEXT,
                    feedback_type TEXT,
                    applied BOOLEAN DEFAULT FALSE,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Erro ao configurar base de dados: {e}")
    
    def process_command(self, user_input):
        """Processa comando do usuário com IA inteligente"""
        try:
            self.logger.info(f"IA processando: {user_input}")
            
            # Verificar se é um comando de aprendizado
            if self._is_learning_command(user_input):
                return self._handle_learning_command(user_input)
            
            # Verificar se é uma correção
            if self._is_correction(user_input):
                return self._handle_correction(user_input)
            
            # Verificar se é resposta a esclarecimento
            if self.pending_clarification:
                return self._handle_clarification_response(user_input)
            
            # Usar NLP para entender comando
            understanding = self.nlp_engine.understand_command(user_input)
            
            if not understanding['understood']:
                # Não entendeu - pedir esclarecimento
                self.pending_clarification = {
                    'original_input': user_input,
                    'timestamp': datetime.now().isoformat()
                }
                
                return {
                    'text': understanding['response'],
                    'action': None,
                    'parameters': {},
                    'needs_clarification': True
                }
            
            # Entendeu - processar comando
            result = self._execute_understood_command(understanding)
            
            # Salvar contexto
            self._add_to_context(user_input, result)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Erro no processamento inteligente: {e}")
            return {
                'text': "Desculpe, tive um problema interno. Pode tentar novamente?",
                'action': None,
                'parameters': {}
            }
    
    def _is_learning_command(self, user_input):
        """Verifica se é comando de aprendizado"""
        learning_triggers = [
            'quando eu disser',
            'aprenda que',
            'lembre-se que',
            'quero que você',
            'ensino você'
        ]
        
        user_lower = user_input.lower()
        return any(trigger in user_lower for trigger in learning_triggers)
    
    def _handle_learning_command(self, user_input):
        """Processa comando de aprendizado"""
        try:
            self.logger.info(f"Processando aprendizado: {user_input}")
            
            # Padrões de aprendizado
            patterns = [
                r'quando eu disser["\']?(.+?)["\']?,?\s+(?:quero que )?(?:você )?(.+)',
                r'aprenda que quando eu falar["\']?(.+?)["\']?,?\s+(?:você deve|faça|execute)\s+(.+)',
                r'lembre-se que["\']?(.+?)["\']?\s+significa\s+(.+)'
            ]
            
            import re
            for pattern in patterns:
                match = re.search(pattern, user_input, re.IGNORECASE)
                if match:
                    trigger_phrase = match.group(1).strip()
                    intended_action = match.group(2).strip()
                    
                    # Processar ação pretendida
                    action_info = self._parse_intended_action(intended_action)
                    
                    # Salvar aprendizado
                    success = self._save_learned_command(trigger_phrase, action_info)
                    
                    if success:
                        # Ensinar ao NLP engine também
                        self.nlp_engine.learn_new_pattern(
                            trigger_phrase, 
                            action_info['action_type'], 
                            action_info.get('parameters')
                        )
                        
                        return {
                            'text': f"Perfeito! Aprendi que quando você disser '{trigger_phrase}', devo {intended_action}. Pode testar agora!",
                            'action': None,
                            'parameters': {},
                            'learned': True
                        }
                    else:
                        return {
                            'text': "Tive um problema para aprender isso. Pode tentar explicar de outra forma?",
                            'action': None,
                            'parameters': {}
                        }
            
            # Se não conseguiu extrair padrão
            return {
                'text': "Entendi que você quer me ensinar algo, mas não consegui captar exatamente o que. Pode explicar assim: 'Quando eu disser X, quero que você faça Y'?",
                'action': None,
                'parameters': {}
            }
            
        except Exception as e:
            self.logger.error(f"Erro no aprendizado: {e}")
            return {
                'text': "Tive um problema para aprender isso. Pode tentar novamente?",
                'action': None,
                'parameters': {}
            }
    
    def _parse_intended_action(self, action_text):
        """Analisa ação pretendida pelo usuário"""
        action_text = action_text.lower().strip()
        
        # Padrões de ação
        if any(word in action_text for word in ['abrir', 'abra', 'execute', 'rode']):
            # Extrair nome do aplicativo
            import re
            app_match = re.search(r'(?:abrir?|execute?|rode?)\s+(?:o\s+)?(.+)', action_text)
            if app_match:
                app_name = app_match.group(1).strip()
                return {
                    'action_type': 'open_application',
                    'parameters': {'app': app_name},
                    'description': f'Abrir {app_name}'
                }
        
        elif any(word in action_text for word in ['pesquisar', 'procurar', 'buscar']):
            return {
                'action_type': 'web_search',
                'parameters': {},
                'description': 'Pesquisar na web'
            }
        
        elif any(word in action_text for word in ['calcular', 'resolver', 'fazer conta']):
            return {
                'action_type': 'math_operation',
                'parameters': {},
                'description': 'Fazer cálculo matemático'
            }
        
        elif any(word in action_text for word in ['volume', 'som', 'áudio']):
            if 'aumentar' in action_text or 'subir' in action_text:
                return {
                    'action_type': 'volume_up',
                    'parameters': {},
                    'description': 'Aumentar volume'
                }
            elif 'diminuir' in action_text or 'baixar' in action_text:
                return {
                    'action_type': 'volume_down',
                    'parameters': {},
                    'description': 'Diminuir volume'
                }
        
        # Ação genérica
        return {
            'action_type': 'custom_action',
            'parameters': {'description': action_text},
            'description': action_text
        }
    
    def _save_learned_command(self, trigger_phrase, action_info):
        """Salva comando aprendido na base de dados"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO learned_commands 
                (trigger_phrase, action_type, parameters, description)
                VALUES (?, ?, ?, ?)
            ''', (
                trigger_phrase,
                action_info['action_type'],
                json.dumps(action_info.get('parameters', {})),
                action_info.get('description', '')
            ))
            
            conn.commit()
            conn.close()
            
            self.logger.info(f"Comando aprendido salvo: {trigger_phrase} -> {action_info['action_type']}")
            return True
            
        except Exception as e:
            self.logger.error(f"Erro ao salvar comando aprendido: {e}")
            return False
    
    def _is_correction(self, user_input):
        """Verifica se é uma correção"""
        correction_triggers = [
            'isso está errado',
            'não é isso',
            'errado',
            'não era isso',
            'você entendeu errado',
            'não queria isso'
        ]
        
        user_lower = user_input.lower()
        return any(trigger in user_lower for trigger in correction_triggers)
    
    def _handle_correction(self, user_input):
        """Processa correção do usuário"""
        try:
            # Buscar última interação
            if not self.conversation_context:
                return {
                    'text': "Não tenho contexto do que corrigir. Pode me explicar o que estava errado?",
                    'action': None,
                    'parameters': {}
                }
            
            last_interaction = self.conversation_context[-1]
            
            # Extrair correção
            import re
            correction_patterns = [
                r'(?:isso está errado|errado|não é isso)\.?\s*(?:o correto é|deveria ser|queria)\s+(.+)',
                r'(?:você entendeu errado|não era isso)\.?\s*(?:eu queria|deveria ser)\s+(.+)',
                r'(?:não queria isso)\.?\s*(?:queria|era para ser)\s+(.+)'
            ]
            
            correction_text = None
            for pattern in correction_patterns:
                match = re.search(pattern, user_input, re.IGNORECASE)
                if match:
                    correction_text = match.group(1).strip()
                    break
            
            if correction_text:
                # Salvar correção
                self._save_correction(
                    last_interaction['user_input'],
                    last_interaction['ai_response'],
                    correction_text
                )
                
                return {
                    'text': f"Entendi! Da próxima vez que você disser '{last_interaction['user_input']}', vou {correction_text}. Obrigada pela correção!",
                    'action': None,
                    'parameters': {},
                    'corrected': True
                }
            else:
                return {
                    'text': "Entendi que algo estava errado, mas não captei a correção. Pode me explicar o que deveria ter feito?",
                    'action': None,
                    'parameters': {}
                }
                
        except Exception as e:
            self.logger.error(f"Erro ao processar correção: {e}")
            return {
                'text': "Tive um problema para processar sua correção. Pode tentar novamente?",
                'action': None,
                'parameters': {}
            }
    
    def _save_correction(self, original_input, wrong_response, correction):
        """Salva correção na base de dados"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO user_feedback 
                (user_input, ai_response, user_correction, feedback_type)
                VALUES (?, ?, ?, ?)
            ''', (original_input, wrong_response, correction, 'correction'))
            
            conn.commit()
            conn.close()
            
            # Aplicar correção ao NLP engine
            self.nlp_engine.learn_new_pattern(original_input, 'custom_action', {'description': correction})
            
        except Exception as e:
            self.logger.error(f"Erro ao salvar correção: {e}")
    
    def _handle_clarification_response(self, user_input):
        """Processa resposta a pedido de esclarecimento"""
        try:
            if not self.pending_clarification:
                return self.process_command(user_input)
            
            original_input = self.pending_clarification['original_input']
            
            # Tentar entender a explicação
            explanation = user_input.strip()
            
            # Salvar como aprendizado
            action_info = self._parse_intended_action(explanation)
            
            # Ensinar ao sistema
            self.nlp_engine.learn_new_pattern(original_input, action_info['action_type'], action_info.get('parameters'))
            
            # Limpar esclarecimento pendente
            self.pending_clarification = None
            
            return {
                'text': f"Perfeito! Agora entendi que '{original_input}' significa {explanation}. Vou lembrar disso! Quer que eu execute agora?",
                'action': action_info['action_type'],
                'parameters': action_info.get('parameters', {}),
                'learned_from_clarification': True
            }
            
        except Exception as e:
            self.logger.error(f"Erro ao processar esclarecimento: {e}")
            self.pending_clarification = None
            return {
                'text': "Tive um problema para entender sua explicação. Pode tentar novamente?",
                'action': None,
                'parameters': {}
            }
    
    def _execute_understood_command(self, understanding):
        """Executa comando que foi entendido"""
        intent = understanding['intent']
        
        if understanding.get('action'):
            return {
                'text': understanding.get('response', 'Executando comando...'),
                'action': understanding['action'],
                'parameters': understanding.get('parameters', {}),
                'confidence': understanding.get('confidence', 1.0)
            }
        else:
            return {
                'text': understanding.get('response', 'Comando processado.'),
                'action': None,
                'parameters': {},
                'confidence': understanding.get('confidence', 1.0)
            }
    
    def _add_to_context(self, user_input, ai_response):
        """Adiciona interação ao contexto"""
        self.conversation_context.append({
            'user_input': user_input,
            'ai_response': ai_response.get('text', ''),
            'timestamp': datetime.now().isoformat()
        })
        
        # Manter apenas últimas 10 interações
        if len(self.conversation_context) > 10:
            self.conversation_context = self.conversation_context[-10:]
    
    def get_learning_stats(self):
        """Retorna estatísticas de aprendizado"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM learned_commands')
            learned_commands = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM user_feedback')
            corrections = cursor.fetchone()[0]
            
            conn.close()
            
            nlp_stats = self.nlp_engine.get_learning_stats()
            
            return {
                'learned_commands': learned_commands,
                'user_corrections': corrections,
                'nlp_patterns': nlp_stats.get('learned_patterns', 0),
                'conversations': nlp_stats.get('conversations', 0),
                'total_interactions': len(self.conversation_context)
            }
            
        except Exception as e:
            self.logger.error(f"Erro ao obter estatísticas: {e}")
            return {}

# Alias para compatibilidade
AIBrain = IntelligentAI
