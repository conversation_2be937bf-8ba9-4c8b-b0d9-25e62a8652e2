"""
SARA AI - Sistema de Reconhecimento de Voz (Versão Segura)
Versão que funciona mesmo sem PyAudio instalado
"""

import time
from config import config
from src.logger import setup_logger

# Tentar importar speech_recognition e pyaudio
try:
    import speech_recognition as sr
    import pyaudio
    VOICE_RECOGNITION_AVAILABLE = True
except ImportError as e:
    VOICE_RECOGNITION_AVAILABLE = False
    IMPORT_ERROR = str(e)

class VoiceRecognitionSafe:
    """Classe para reconhecimento de voz com fallback seguro"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.recognizer = None
        self.microphone = None
        self.is_listening = False
        self.available = VOICE_RECOGNITION_AVAILABLE
        
        if self.available:
            self._setup_recognizer()
        else:
            self.logger.warning(f"Reconhecimento de voz não disponível: {IMPORT_ERROR}")
            self.logger.info("Sistema funcionará apenas com painel web")
        
    def _setup_recognizer(self):
        """Configura o reconhecedor de voz"""
        try:
            self.logger.info("Configurando reconhecedor de voz...")
            
            self.recognizer = sr.Recognizer()
            self.microphone = sr.Microphone()
            
            # Ajustar para ruído ambiente
            with self.microphone as source:
                self.logger.info("Calibrando para ruído ambiente...")
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                
            # Configurações do reconhecedor
            self.recognizer.energy_threshold = 300
            self.recognizer.dynamic_energy_threshold = True
            self.recognizer.pause_threshold = 0.8
            self.recognizer.phrase_threshold = 0.3
            
            self.logger.info("Reconhecedor configurado com sucesso")
            
        except Exception as e:
            self.logger.error(f"Erro ao configurar reconhecedor: {e}")
            self.available = False
    
    def listen(self, timeout=None, phrase_time_limit=5):
        """
        Escuta por comandos de voz (versão segura)
        
        Args:
            timeout: Tempo limite para começar a escutar
            phrase_time_limit: Tempo limite para a frase
            
        Returns:
            str: Texto reconhecido ou None se não houver
        """
        if not self.available:
            # Simular escuta para não quebrar o sistema
            time.sleep(0.5)
            return None
            
        try:
            self.logger.debug("Iniciando escuta...")
            
            with self.microphone as source:
                # Escutar áudio
                audio = self.recognizer.listen(
                    source, 
                    timeout=timeout, 
                    phrase_time_limit=phrase_time_limit
                )
                
            # Converter áudio para texto
            text = self.recognizer.recognize_google(
                audio, 
                language=config.VOICE_RECOGNITION_LANGUAGE
            )
            
            self.logger.debug(f"Texto reconhecido: {text}")
            return text.lower().strip()
            
        except sr.WaitTimeoutError:
            self.logger.debug("Timeout na escuta")
            return None
            
        except sr.UnknownValueError:
            self.logger.debug("Não foi possível entender o áudio")
            return None
            
        except sr.RequestError as e:
            self.logger.error(f"Erro no serviço de reconhecimento: {e}")
            return None
            
        except Exception as e:
            self.logger.error(f"Erro inesperado no reconhecimento: {e}")
            return None
    
    def listen_for_wake_word(self, wake_words=None):
        """
        Escuta continuamente por palavras de ativação (versão segura)
        
        Args:
            wake_words: Lista de palavras de ativação
            
        Returns:
            bool: True se palavra de ativação foi detectada
        """
        if not self.available:
            return False
            
        if wake_words is None:
            wake_words = config.AI_WAKE_WORDS
            
        try:
            text = self.listen(timeout=1, phrase_time_limit=3)
            
            if text:
                for wake_word in wake_words:
                    if wake_word in text:
                        self.logger.info(f"Palavra de ativação detectada: {wake_word}")
                        return True
                        
            return False
            
        except Exception as e:
            self.logger.error(f"Erro na detecção de palavra de ativação: {e}")
            return False
    
    def test_microphone(self):
        """Testa se o microfone está funcionando (versão segura)"""
        if not self.available:
            return False, "Reconhecimento de voz não disponível - PyAudio não instalado"
            
        try:
            self.logger.info("Testando microfone...")
            
            with self.microphone as source:
                self.logger.info("Fale algo para testar o microfone...")
                audio = self.recognizer.listen(source, timeout=5, phrase_time_limit=3)
                
            text = self.recognizer.recognize_google(
                audio, 
                language=config.VOICE_RECOGNITION_LANGUAGE
            )
            
            self.logger.info(f"Teste bem-sucedido! Texto: {text}")
            return True, text
            
        except Exception as e:
            self.logger.error(f"Erro no teste do microfone: {e}")
            return False, str(e)
    
    def is_available(self):
        """Retorna se o reconhecimento de voz está disponível"""
        return self.available
    
    def get_status(self):
        """Retorna status do reconhecimento de voz"""
        if self.available:
            return {
                'available': True,
                'status': 'Funcionando',
                'message': 'Reconhecimento de voz ativo'
            }
        else:
            return {
                'available': False,
                'status': 'Indisponível',
                'message': 'PyAudio não instalado - use o painel web'
            }

# Alias para compatibilidade
VoiceRecognition = VoiceRecognitionSafe
