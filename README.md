# 🤖 SARA AI - Assistente de IA Conversacional

**SARA AI** é um assistente de IA conversacional avançado que pode ser controlado por voz e executar ações no seu computador, como abrir aplicativos, navegar na web e muito mais!

## ✨ Funcionalidades

### 🎤 **Controle por Voz**
- Reconhecimento de fala em português brasileiro
- Síntese de voz natural para respostas
- Palavras de ativação personalizáveis
- Controle de volume e velocidade da fala

### 🧠 **IA Conversacional**
- Integração com OpenAI GPT para conversas naturais
- Processamento de linguagem natural avançado
- Histórico de conversas persistente
- Personalidade configurável

### 💻 **Controle de Sistema**
- Abrir aplicativos (Opera, Chrome, Calculadora, etc.)
- Controlar volume do sistema
- Navegar em sites
- Realizar pesquisas na web
- Controles de mídia (play/pause)
- Abrir jogos do Windows

### 🌐 **Painel Web de Controle**
- Interface web moderna e responsiva
- Monitoramento em tempo real
- Histórico de comandos e respostas
- Configurações personalizáveis
- Logs detalhados do sistema
- Testes de voz integrados

## 🚀 Instalação Rápida

### 1. **Pré-requisitos**
- Python 3.8 ou superior
- Windows 10/11 (recomendado)
- Microfone funcionando
- Conexão com internet

### 2. **Instalação Automática**
```bash
# Clone o repositório
git clone https://github.com/seu-usuario/sara-ai.git
cd sara-ai

# Execute o instalador
python install.py
```

### 3. **Configuração**
1. Abra o arquivo `.env`
2. Adicione sua chave da OpenAI:
   ```
   OPENAI_API_KEY=sua_chave_openai_aqui
   ```
3. Obtenha sua chave em: https://platform.openai.com/api-keys

### 4. **Execução**
```bash
python main.py
```

## 🎯 Como Usar

### **Comandos de Voz**
- **"Hey Sara, abrir Opera"** - Abre o navegador Opera
- **"Sara, pesquisar clima hoje"** - Pesquisa o clima no Google
- **"Abrir calculadora"** - Abre a calculadora do Windows
- **"Volume up"** - Aumenta o volume do sistema
- **"Tocar música"** - Controla reprodução de mídia

### **Painel Web**
1. Acesse: `http://localhost:5000`
2. Monitore status em tempo real
3. Envie comandos via interface web
4. Configure preferências de voz
5. Visualize logs e histórico

## 📁 Estrutura do Projeto

```
sara_ai/
├── main.py                 # Arquivo principal
├── config.py              # Configurações do sistema
├── requirements.txt       # Dependências Python
├── install.py            # Script de instalação
├── .env.example          # Exemplo de variáveis de ambiente
├── src/                  # Código fonte principal
│   ├── voice_recognition.py    # Reconhecimento de voz
│   ├── voice_synthesis.py      # Síntese de voz
│   ├── ai_brain.py            # Motor de IA
│   ├── system_controller.py   # Controle de sistema
│   └── logger.py              # Sistema de logging
├── web/                  # Interface web
│   ├── app.py           # Servidor Flask
│   ├── templates/       # Templates HTML
│   └── static/          # CSS e JavaScript
└── logs/                # Arquivos de log
```

## ⚙️ Configuração Avançada

### **Personalizar Comandos**
Edite `config.py` para adicionar novos comandos:

```python
SYSTEM_COMMANDS = {
    "meu_app": {
        "command": "C:\\caminho\\para\\app.exe",
        "args": []
    }
}
```

### **Configurar Voz**
```python
VOICE_RECOGNITION_LANGUAGE = "pt-BR"  # Idioma do reconhecimento
VOICE_RATE = 150                      # Velocidade da fala
VOICE_VOLUME = 0.8                    # Volume (0.0 a 1.0)
```

### **Personalizar IA**
```python
AI_PERSONALITY = """
Você é SARA, um assistente brasileiro amigável.
Seja sempre educada e eficiente.
"""
```

## 🔧 Solução de Problemas

### **Erro de Microfone**
- Verifique permissões do microfone
- Teste com: `python -c "import speech_recognition as sr; print('OK')"`
- Configure microfone padrão no Windows

### **Erro OpenAI**
- Verifique se a chave está correta no `.env`
- Confirme saldo na conta OpenAI
- Teste conexão com internet

### **Aplicativos não abrem**
- Verifique caminhos em `config.py`
- Execute como administrador se necessário
- Confirme se aplicativo está instalado

## 🛡️ Segurança

- Comandos perigosos são bloqueados automaticamente
- Lista de comandos permitidos configurável
- Logs detalhados de todas as ações
- Execução em ambiente isolado

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch: `git checkout -b minha-feature`
3. Commit: `git commit -m 'Adiciona nova feature'`
4. Push: `git push origin minha-feature`
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para detalhes.

## 🆘 Suporte

- **Issues**: https://github.com/seu-usuario/sara-ai/issues
- **Documentação**: https://github.com/seu-usuario/sara-ai/wiki
- **Email**: <EMAIL>

## 🎉 Agradecimentos

- OpenAI pela API GPT
- Comunidade Python
- Contribuidores do projeto

---

**Desenvolvido com ❤️ para tornar a interação com computadores mais natural e intuitiva!**
