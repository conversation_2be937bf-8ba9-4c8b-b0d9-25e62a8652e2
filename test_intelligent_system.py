"""
SARA AI - Teste do Sistema Inteligente
Testa o novo sistema de IA que realmente aprende
"""

import sys
import os

def test_nlp_engine():
    """Testa motor de NLP avançado"""
    print("🧪 Testando Motor NLP Avançado...")
    try:
        from src.nlp_engine import AdvancedNLPEngine
        
        nlp = AdvancedNLPEngine()
        
        # Testar comandos variados
        test_commands = [
            "abrir calculadora",
            "abra calculadora", 
            "SARA, de forma imediata, abra a calculadora",
            "execute a calculadora",
            "que horas são",
            "qual o horário atual",
            "quanto é 2 x 2",
            "calcule 5 + 3",
            "pesquisar clima hoje"
        ]
        
        print("   Testando entendimento de comandos:")
        for cmd in test_commands:
            result = nlp.understand_command(cmd)
            status = "✅" if result['understood'] else "❌"
            print(f"   {status} '{cmd}' -> {result['intent']} (conf: {result['confidence']:.2f})")
        
        print("✅ Motor NLP funcionando")
        return True
        
    except Exception as e:
        print(f"❌ Erro no NLP: {e}")
        return False

def test_intelligent_ai():
    """Testa IA inteligente"""
    print("🧪 Testando IA Inteligente...")
    try:
        from src.intelligent_ai import IntelligentAI
        
        ai = IntelligentAI()
        
        # Testar comandos básicos
        test_commands = [
            "olá sara",
            "abrir calculadora",
            "que horas são",
            "quanto é 2 + 2"
        ]
        
        print("   Testando processamento inteligente:")
        for cmd in test_commands:
            response = ai.process_command(cmd)
            print(f"   📝 '{cmd}' -> '{response['text']}'")
            if response.get('action'):
                print(f"      🎯 Ação: {response['action']} {response.get('parameters', {})}")
        
        # Testar aprendizado
        print("   Testando aprendizado:")
        learn_cmd = "quando eu disser 'abrir voicemod', quero que você abra o VoiceMod"
        response = ai.process_command(learn_cmd)
        print(f"   📚 Aprendizado: '{response['text']}'")
        
        # Testar comando aprendido
        test_learned = "abrir voicemod"
        response = ai.process_command(test_learned)
        print(f"   🧠 Comando aprendido: '{response['text']}'")
        
        # Estatísticas
        stats = ai.get_learning_stats()
        print(f"   📊 Stats: {stats}")
        
        print("✅ IA Inteligente funcionando")
        return True
        
    except Exception as e:
        print(f"❌ Erro na IA: {e}")
        return False

def test_voice_synthesis():
    """Testa síntese de voz melhorada"""
    print("🧪 Testando Síntese de Voz Melhorada...")
    try:
        from src.voice_synthesis_advanced import AdvancedVoiceSynthesis
        
        tts = AdvancedVoiceSynthesis()
        
        # Mostrar engines
        engines = tts.get_available_engines()
        print(f"   Engines: {list(engines.keys())}")
        print(f"   Engine atual: {tts.current_engine}")
        
        # Testar fala múltipla
        print("   Testando fala múltipla...")
        tts.speak("Primeira frase de teste.")
        tts.speak("Segunda frase de teste.")
        tts.speak("Terceira frase de teste.")
        
        print("   ⏳ Aguardando conclusão da fala...")
        import time
        time.sleep(8)  # Aguardar fala terminar
        
        print("✅ Síntese de voz melhorada funcionando")
        return True
        
    except Exception as e:
        print(f"❌ Erro na síntese: {e}")
        return False

def test_integration():
    """Testa integração completa"""
    print("🧪 Testando Integração Completa...")
    try:
        from src.intelligent_ai import IntelligentAI
        from src.voice_synthesis_advanced import AdvancedVoiceSynthesis
        from src.system_controller import SystemController
        
        # Criar componentes
        ai = IntelligentAI()
        tts = AdvancedVoiceSynthesis()
        controller = SystemController()
        
        # Simular fluxo completo
        print("   Simulando fluxo completo:")
        
        # 1. Comando natural
        user_input = "SARA, de forma imediata, abra a calculadora"
        print(f"   👤 Usuário: {user_input}")
        
        # 2. IA processa
        response = ai.process_command(user_input)
        print(f"   🧠 IA: {response['text']}")
        
        # 3. Executar ação se houver
        if response.get('action'):
            action_result = controller.execute_action(
                response['action'], 
                response.get('parameters', {})
            )
            print(f"   💻 Sistema: {action_result}")
        
        # 4. Falar resposta
        tts.speak(response['text'])
        print("   🗣️ SARA falou a resposta")
        
        print("✅ Integração completa funcionando")
        return True
        
    except Exception as e:
        print(f"❌ Erro na integração: {e}")
        return False

def main():
    """Função principal de teste"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║              SARA AI - TESTE SISTEMA INTELIGENTE             ║
    ║           Testando IA que realmente aprende                  ║
    ╚══════════════════════════════════════════════════════════════╝
    """)
    
    tests = [
        ("Motor NLP Avançado", test_nlp_engine),
        ("IA Inteligente", test_intelligent_ai),
        ("Síntese de Voz Melhorada", test_voice_synthesis),
        ("Integração Completa", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"💥 Erro inesperado em {test_name}: {e}")
            results.append((test_name, False))
    
    # Resumo
    print("\n" + "="*60)
    print("RESUMO DOS TESTES")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        print(f"{test_name:.<40} {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Resultado: {passed}/{len(results)} testes passaram")
    
    if passed == len(results):
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("   O Sistema Inteligente está funcionando!")
        print("\n🚀 Exemplos de uso:")
        print("   • 'abrir calculadora' ou 'abra calculadora' ou 'SARA, abra a calculadora'")
        print("   • 'que horas são' ou 'qual o horário atual'")
        print("   • 'quanto é 2 x 2' ou 'calcule 5 + 3'")
        print("   • 'quando eu disser X, quero que você faça Y' (para ensinar)")
        print("   • 'isso está errado, o correto é...' (para corrigir)")
    else:
        print("\n⚠️  ALGUNS TESTES FALHARAM")
        print("   Verifique os erros acima")

if __name__ == "__main__":
    try:
        main()
        input("\nPressione Enter para sair...")
    except KeyboardInterrupt:
        print("\n\nTestes cancelados pelo usuário")
    except Exception as e:
        print(f"\nErro inesperado: {e}")
        input("Pressione Enter para sair...")
