"""
SARA AI - Script de Teste
Testa componentes principais do sistema
"""

import sys
import os
import time
from src.logger import setup_logger
from src.voice_synthesis import VoiceSynthesis
from src.ai_brain import AIBrain
from src.system_controller import SystemController
from config import config

def test_banner():
    """Exibe banner de teste"""
    print("""
    ╔══════════════════════════════════════════════════════════════╗
    ║                    SARA AI - TESTES                          ║
    ║              Verificação dos Componentes                     ║
    ╚══════════════════════════════════════════════════════════════╝
    """)

def test_logger():
    """Testa sistema de logging"""
    print("🧪 Testando sistema de logging...")
    try:
        logger = setup_logger("test")
        logger.info("Teste de log INFO")
        logger.warning("Teste de log WARNING")
        logger.error("Teste de log ERROR")
        print("✅ Sistema de logging funcionando")
        return True
    except Exception as e:
        print(f"❌ Erro no sistema de logging: {e}")
        return False

def test_voice_synthesis():
    """Testa síntese de voz"""
    print("🧪 Testando síntese de voz...")
    try:
        tts = VoiceSynthesis()
        
        # Teste básico
        if tts.test_speech("Teste de síntese de voz da SARA AI"):
            print("✅ Síntese de voz funcionando")
            return True
        else:
            print("❌ Erro na síntese de voz")
            return False
            
    except Exception as e:
        print(f"❌ Erro na síntese de voz: {e}")
        return False

def test_ai_brain():
    """Testa motor de IA"""
    print("🧪 Testando motor de IA...")
    try:
        ai = AIBrain()
        
        # Teste de comando simples
        response = ai.process_command("olá")
        if response and response.get('text'):
            print(f"   Resposta: {response['text']}")
            print("✅ Motor de IA funcionando")
            return True
        else:
            print("❌ Erro no motor de IA")
            return False
            
    except Exception as e:
        print(f"❌ Erro no motor de IA: {e}")
        return False

def test_system_controller():
    """Testa controlador de sistema"""
    print("🧪 Testando controlador de sistema...")
    try:
        controller = SystemController()
        
        # Teste de informações do sistema
        info = controller.get_system_info()
        if info:
            print(f"   CPU: {info.get('cpu_percent', 'N/A')}%")
            print("✅ Controlador de sistema funcionando")
            return True
        else:
            print("❌ Erro no controlador de sistema")
            return False
            
    except Exception as e:
        print(f"❌ Erro no controlador de sistema: {e}")
        return False

def test_voice_recognition():
    """Testa reconhecimento de voz (versão segura)"""
    print("🧪 Testando reconhecimento de voz...")
    try:
        from src.voice_recognition_safe import VoiceRecognitionSafe

        vr = VoiceRecognitionSafe()

        if vr.is_available():
            # Teste do microfone
            success, result = vr.test_microphone()
            if success:
                print(f"   Texto reconhecido: {result}")
                print("✅ Reconhecimento de voz funcionando")
                return True
            else:
                print(f"⚠️  Problema no reconhecimento de voz: {result}")
                print("   Sistema funcionará apenas com painel web")
                return True
        else:
            status = vr.get_status()
            print(f"⚠️  {status['message']}")
            print("   Sistema funcionará apenas com painel web")
            return True  # Não é erro crítico

    except Exception as e:
        print(f"⚠️  Erro no reconhecimento de voz: {e}")
        print("   Sistema funcionará apenas com painel web")
        return True  # Não é erro crítico

def test_web_app():
    """Testa aplicação web"""
    print("🧪 Testando aplicação web...")
    try:
        from web.app import create_app
        
        app = create_app()
        if app:
            print("✅ Aplicação web criada com sucesso")
            return True
        else:
            print("❌ Erro na aplicação web")
            return False
            
    except Exception as e:
        print(f"❌ Erro na aplicação web: {e}")
        return False

def test_configuration():
    """Testa configurações"""
    print("🧪 Testando configurações...")
    try:
        # Verificar configurações básicas
        if hasattr(config, 'APP_NAME') and config.APP_NAME:
            print(f"   App: {config.APP_NAME} v{config.VERSION}")
            print("✅ Configurações carregadas")
            return True
        else:
            print("❌ Erro nas configurações")
            return False
            
    except Exception as e:
        print(f"❌ Erro nas configurações: {e}")
        return False

def test_dependencies():
    """Testa dependências principais"""
    print("🧪 Testando dependências...")
    
    dependencies = [
        'speech_recognition',
        'pyttsx3',
        'flask',
        'pyautogui',
        'psutil',
        'openai'
    ]
    
    failed = []
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"   ✅ {dep}")
        except ImportError:
            print(f"   ❌ {dep}")
            failed.append(dep)
    
    if failed:
        print(f"⚠️  Dependências faltando: {', '.join(failed)}")
        return False
    else:
        print("✅ Todas as dependências disponíveis")
        return True

def run_integration_test():
    """Executa teste de integração básico"""
    print("🧪 Teste de integração...")
    try:
        # Criar componentes
        ai = AIBrain()
        controller = SystemController()
        tts = VoiceSynthesis()
        
        # Simular comando
        command = "olá sara"
        response = ai.process_command(command)
        
        if response and response.get('text'):
            print(f"   Comando: {command}")
            print(f"   Resposta: {response['text']}")
            
            # Falar resposta (opcional)
            tts.speak(response['text'])
            
            print("✅ Teste de integração passou")
            return True
        else:
            print("❌ Falha no teste de integração")
            return False
            
    except Exception as e:
        print(f"❌ Erro no teste de integração: {e}")
        return False

def main():
    """Função principal de teste"""
    test_banner()
    
    tests = [
        ("Dependências", test_dependencies),
        ("Configurações", test_configuration),
        ("Logger", test_logger),
        ("Síntese de Voz", test_voice_synthesis),
        ("Motor de IA", test_ai_brain),
        ("Controlador de Sistema", test_system_controller),
        ("Reconhecimento de Voz", test_voice_recognition),
        ("Aplicação Web", test_web_app),
        ("Integração", run_integration_test)
    ]
    
    results = []
    
    print("\n" + "="*60)
    print("EXECUTANDO TESTES...")
    print("="*60)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"💥 Erro inesperado em {test_name}: {e}")
            results.append((test_name, False))
        
        time.sleep(0.5)  # Pequena pausa entre testes
    
    # Resumo dos resultados
    print("\n" + "="*60)
    print("RESUMO DOS TESTES")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSOU" if result else "❌ FALHOU"
        print(f"{test_name:.<30} {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Resultado: {passed}/{total} testes passaram")
    
    if passed == total:
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("   O SARA AI está pronto para uso!")
    elif passed >= total * 0.8:
        print("\n⚠️  MAIORIA DOS TESTES PASSOU")
        print("   O SARA AI deve funcionar, mas verifique os erros")
    else:
        print("\n💥 MUITOS TESTES FALHARAM")
        print("   Verifique a instalação antes de usar")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n{'='*60}")
        if success:
            print("🚀 Execute 'python main.py' para iniciar o SARA AI!")
        else:
            print("🔧 Corrija os problemas antes de executar o sistema")
        print("="*60)
        
        input("\nPressione Enter para sair...")
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Testes cancelados pelo usuário")
    except Exception as e:
        print(f"\n💥 Erro inesperado nos testes: {e}")
        input("\nPressione Enter para sair...")
