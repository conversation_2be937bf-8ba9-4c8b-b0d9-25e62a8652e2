"""
SARA AI - Controlador de Sistema
Executa ações no sistema operacional como abrir aplicativos, controlar volume, etc.
"""

import subprocess
import os
import webbrowser
import pyautogui
import psutil
import time
from config import config
from src.logger import setup_logger

class SystemController:
    """Classe para controle de sistema e automação"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.running_processes = {}
        
        # Configurar pyautogui
        pyautogui.FAILSAFE = True
        pyautogui.PAUSE = 0.5
        
    def execute_action(self, action, parameters):
        """
        Executa uma ação no sistema
        
        Args:
            action: Tipo de ação a executar
            parameters: Parâmetros da ação
            
        Returns:
            dict: Resultado da execução
        """
        try:
            self.logger.info(f"Executando ação: {action} com parâmetros: {parameters}")
            
            # Verificar se ação é permitida
            if not self._is_action_allowed(action):
                return {
                    'success': False,
                    'error': f'Ação {action} não é permitida por segurança'
                }
            
            # Executar ação específica
            action_methods = {
                'open_application': self._open_application,
                'close_application': self._close_application,
                'open_website': self._open_website,
                'web_search': self._web_search,
                'volume_up': self._volume_up,
                'volume_down': self._volume_down,
                'volume_mute': self._volume_mute,
                'play_music': self._play_music,
                'pause_music': self._pause_music,
                'open_game': self._open_game,
                'execute_command': self._execute_command,
                'close_active_window': self._close_active_window
            }
            
            if action in action_methods:
                return action_methods[action](parameters)
            else:
                return {
                    'success': False,
                    'error': f'Ação {action} não reconhecida'
                }
                
        except Exception as e:
            self.logger.error(f"Erro ao executar ação {action}: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _is_action_allowed(self, action):
        """Verifica se a ação é permitida por segurança"""
        # Verificar comandos bloqueados
        for blocked in config.BLOCKED_COMMANDS:
            if blocked in action.lower():
                return False
        
        return True
    
    def _open_application(self, parameters):
        """Abre uma aplicação"""
        try:
            app = parameters.get('app', '').lower()

            # Tentar caminhos comuns para aplicativos populares
            common_apps = {
                'opera': [
                    'opera.exe',
                    'C:\\Program Files\\Opera\\opera.exe',
                    'C:\\Program Files (x86)\\Opera\\opera.exe',
                    f'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Opera\\opera.exe'
                ],
                'chrome': [
                    'chrome.exe',
                    'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
                    'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe'
                ],
                'firefox': [
                    'firefox.exe',
                    'C:\\Program Files\\Mozilla Firefox\\firefox.exe',
                    'C:\\Program Files (x86)\\Mozilla Firefox\\firefox.exe'
                ],
                'notepad': ['notepad.exe'],
                'calculator': ['calc.exe'],
                'paint': ['mspaint.exe'],
                'wordpad': ['wordpad.exe']
            }

            if app in common_apps:
                # Tentar cada caminho possível
                for command_path in common_apps[app]:
                    try:
                        if os.path.exists(command_path) or not command_path.startswith('C:'):
                            process = subprocess.Popen([command_path])
                            self.running_processes[app] = process

                            self.logger.info(f"Aplicativo {app} aberto com sucesso: {command_path}")
                            return {'success': True, 'message': f'Aplicativo {app} aberto com sucesso!'}
                    except Exception as e:
                        self.logger.debug(f"Falha ao tentar {command_path}: {e}")
                        continue

                # Se chegou aqui, nenhum caminho funcionou
                return {'success': False, 'error': f'Aplicativo {app} não encontrado no sistema'}

            elif app in config.SYSTEM_COMMANDS:
                # Usar configuração personalizada
                command_info = config.SYSTEM_COMMANDS[app]
                command = command_info['command']
                args = command_info.get('args', [])

                # Substituir placeholder do username
                if '{username}' in command:
                    username = os.getenv('USERNAME', 'User')
                    command = command.replace('{username}', username)

                # Executar comando
                if os.path.exists(command) or not command.startswith('C:'):
                    process = subprocess.Popen([command] + args)
                    self.running_processes[app] = process

                    self.logger.info(f"Aplicativo {app} aberto com sucesso")
                    return {'success': True, 'message': f'Aplicativo {app} aberto'}
                else:
                    return {'success': False, 'error': f'Aplicativo {app} não encontrado'}

            else:
                # Tentar como comando genérico do sistema
                try:
                    process = subprocess.Popen([app + '.exe'])
                    self.running_processes[app] = process
                    return {'success': True, 'message': f'Aplicativo {app} aberto'}
                except:
                    return {'success': False, 'error': f'Aplicativo {app} não encontrado'}

        except Exception as e:
            self.logger.error(f"Erro ao abrir aplicativo: {e}")
            return {'success': False, 'error': str(e)}
    
    def _open_generic_app(self, parameters):
        """Tenta abrir aplicativo genérico"""
        try:
            # Lista de locais comuns para aplicativos
            common_paths = [
                "C:\\Program Files\\",
                "C:\\Program Files (x86)\\",
                f"C:\\Users\\<USER>\\AppData\\Local\\Programs\\",
                f"C:\\Users\\<USER>\\AppData\\Roaming\\Microsoft\\Windows\\Start Menu\\Programs\\"
            ]
            
            # Tentar encontrar e abrir aplicativo
            # Por enquanto, retorna erro - implementação futura
            return {'success': False, 'error': 'Aplicativo genérico não implementado ainda'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _close_application(self, parameters):
        """Fecha uma aplicação"""
        try:
            app = parameters.get('app', '').lower()
            
            # Tentar fechar processo conhecido
            if app in self.running_processes:
                process = self.running_processes[app]
                process.terminate()
                del self.running_processes[app]
                
                self.logger.info(f"Aplicativo {app} fechado")
                return {'success': True, 'message': f'Aplicativo {app} fechado'}
            
            # Tentar encontrar e fechar processo por nome
            app_names = {
                'opera': 'opera.exe',
                'chrome': 'chrome.exe',
                'notepad': 'notepad.exe',
                'calculator': 'calc.exe'
            }
            
            if app in app_names:
                process_name = app_names[app]
                killed = False
                
                for proc in psutil.process_iter(['pid', 'name']):
                    if proc.info['name'].lower() == process_name:
                        proc.kill()
                        killed = True
                
                if killed:
                    return {'success': True, 'message': f'Aplicativo {app} fechado'}
                else:
                    return {'success': False, 'error': f'Aplicativo {app} não está rodando'}
            
            return {'success': False, 'error': f'Não foi possível fechar {app}'}
            
        except Exception as e:
            self.logger.error(f"Erro ao fechar aplicativo: {e}")
            return {'success': False, 'error': str(e)}
    
    def _open_website(self, parameters):
        """Abre um site no navegador"""
        try:
            url = parameters.get('url', 'google.com')
            
            # Garantir que URL tem protocolo
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            
            webbrowser.open(url)
            
            self.logger.info(f"Site aberto: {url}")
            return {'success': True, 'message': f'Site {url} aberto'}
            
        except Exception as e:
            self.logger.error(f"Erro ao abrir site: {e}")
            return {'success': False, 'error': str(e)}
    
    def _web_search(self, parameters):
        """Realiza pesquisa na web"""
        try:
            query = parameters.get('query', '')
            
            if not query:
                return {'success': False, 'error': 'Consulta de pesquisa vazia'}
            
            # Criar URL de pesquisa do Google
            search_url = f"https://www.google.com/search?q={query.replace(' ', '+')}"
            webbrowser.open(search_url)
            
            self.logger.info(f"Pesquisa realizada: {query}")
            return {'success': True, 'message': f'Pesquisando por: {query}'}
            
        except Exception as e:
            self.logger.error(f"Erro na pesquisa web: {e}")
            return {'success': False, 'error': str(e)}
    
    def _volume_up(self, parameters):
        """Aumenta o volume do sistema"""
        try:
            # Usar teclas de atalho do Windows
            pyautogui.press('volumeup')
            
            self.logger.info("Volume aumentado")
            return {'success': True, 'message': 'Volume aumentado'}
            
        except Exception as e:
            self.logger.error(f"Erro ao aumentar volume: {e}")
            return {'success': False, 'error': str(e)}
    
    def _volume_down(self, parameters):
        """Diminui o volume do sistema"""
        try:
            pyautogui.press('volumedown')
            
            self.logger.info("Volume diminuído")
            return {'success': True, 'message': 'Volume diminuído'}
            
        except Exception as e:
            self.logger.error(f"Erro ao diminuir volume: {e}")
            return {'success': False, 'error': str(e)}
    
    def _volume_mute(self, parameters):
        """Silencia/desilencia o sistema"""
        try:
            pyautogui.press('volumemute')
            
            self.logger.info("Volume silenciado/desilenciado")
            return {'success': True, 'message': 'Volume silenciado/desilenciado'}
            
        except Exception as e:
            self.logger.error(f"Erro ao silenciar volume: {e}")
            return {'success': False, 'error': str(e)}
    
    def _play_music(self, parameters):
        """Reproduz música (controle de mídia)"""
        try:
            pyautogui.press('playpause')
            
            self.logger.info("Música reproduzida/pausada")
            return {'success': True, 'message': 'Música reproduzida/pausada'}
            
        except Exception as e:
            self.logger.error(f"Erro ao controlar música: {e}")
            return {'success': False, 'error': str(e)}
    
    def _pause_music(self, parameters):
        """Pausa música (controle de mídia)"""
        try:
            pyautogui.press('playpause')
            
            self.logger.info("Música pausada")
            return {'success': True, 'message': 'Música pausada'}
            
        except Exception as e:
            self.logger.error(f"Erro ao pausar música: {e}")
            return {'success': False, 'error': str(e)}
    
    def _open_game(self, parameters):
        """Abre um jogo"""
        try:
            game = parameters.get('game', '').lower()
            
            # Jogos do Windows
            windows_games = {
                'solitaire': 'ms-windows-store://pdp/?productid=9WZDNCRFHWD2',
                'minesweeper': 'ms-windows-store://pdp/?productid=9WZDNCRFHWCN'
            }
            
            if game in windows_games:
                os.startfile(windows_games[game])
                return {'success': True, 'message': f'Jogo {game} aberto'}
            else:
                return {'success': False, 'error': f'Jogo {game} não encontrado'}
                
        except Exception as e:
            self.logger.error(f"Erro ao abrir jogo: {e}")
            return {'success': False, 'error': str(e)}
    
    def _execute_command(self, parameters):
        """Executa comando genérico (com restrições de segurança)"""
        try:
            command = parameters.get('command', '')
            
            # Verificações de segurança básicas
            dangerous_commands = ['del', 'rm', 'format', 'shutdown', 'restart', 'rmdir']
            
            if any(dangerous in command.lower() for dangerous in dangerous_commands):
                return {'success': False, 'error': 'Comando perigoso bloqueado por segurança'}
            
            # Por enquanto, não executar comandos arbitrários
            return {'success': False, 'error': 'Execução de comandos arbitrários desabilitada por segurança'}
            
        except Exception as e:
            self.logger.error(f"Erro ao executar comando: {e}")
            return {'success': False, 'error': str(e)}
    
    def _close_active_window(self, parameters):
        """Fecha a janela ativa"""
        try:
            pyautogui.hotkey('alt', 'f4')
            
            self.logger.info("Janela ativa fechada")
            return {'success': True, 'message': 'Janela ativa fechada'}
            
        except Exception as e:
            self.logger.error(f"Erro ao fechar janela: {e}")
            return {'success': False, 'error': str(e)}
    
    def get_running_processes(self):
        """Retorna lista de processos em execução"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                processes.append(proc.info)
            
            return processes
            
        except Exception as e:
            self.logger.error(f"Erro ao obter processos: {e}")
            return []
    
    def get_system_info(self):
        """Retorna informações do sistema"""
        try:
            info = {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory': psutil.virtual_memory()._asdict(),
                'disk': psutil.disk_usage('/')._asdict(),
                'boot_time': psutil.boot_time()
            }
            
            return info
            
        except Exception as e:
            self.logger.error(f"Erro ao obter info do sistema: {e}")
            return {}
