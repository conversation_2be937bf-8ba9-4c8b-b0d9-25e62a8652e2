"""
SARA AI - Motor de Processamento de Linguagem Natural Avançado
Sistema que realmente entende comandos naturais e aprende padrões
"""

import re
import json
import sqlite3
import os
from collections import defaultdict, Counter
from difflib import SequenceMatcher
from datetime import datetime
import math
from src.logger import setup_logger

# Tentar importar bibliotecas avançadas
try:
    import nltk
    from nltk.tokenize import word_tokenize
    from nltk.corpus import stopwords
    from nltk.stem import SnowballStemmer
    NLTK_AVAILABLE = True
except ImportError:
    NLTK_AVAILABLE = False

class AdvancedNLPEngine:
    """Motor de NLP que realmente entende linguagem natural"""
    
    def __init__(self):
        self.logger = setup_logger(__name__)
        self.db_path = "data/nlp_patterns.db"
        
        # Configurar NLTK se disponível
        self._setup_nltk()
        
        # Padrões de comando aprendidos
        self.command_patterns = defaultdict(list)
        self.intent_patterns = defaultdict(list)
        self.response_templates = {}
        
        # Configurar base de dados
        self._setup_database()
        
        # Carregar padrões base
        self._load_base_patterns()
        
        # Carregar padrões aprendidos
        self._load_learned_patterns()
        
        self.logger.info("Motor NLP avançado inicializado")
    
    def _setup_nltk(self):
        """Configura NLTK para processamento avançado"""
        if not NLTK_AVAILABLE:
            self.logger.warning("NLTK não disponível - usando processamento básico")
            return
        
        try:
            # Baixar recursos necessários
            resources = ['punkt', 'stopwords', 'rslp']
            for resource in resources:
                try:
                    nltk.data.find(f'tokenizers/{resource}')
                except LookupError:
                    try:
                        nltk.data.find(f'corpora/{resource}')
                    except LookupError:
                        nltk.download(resource, quiet=True)
            
            # Configurar stemmer para português
            self.stemmer = SnowballStemmer('portuguese')
            self.stop_words = set(stopwords.words('portuguese'))
            
            self.logger.info("NLTK configurado para português")
            
        except Exception as e:
            self.logger.error(f"Erro ao configurar NLTK: {e}")
    
    def _setup_database(self):
        """Configura base de dados para padrões NLP"""
        try:
            os.makedirs("data", exist_ok=True)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Tabela de padrões de comando
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS command_patterns (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    original_phrase TEXT NOT NULL,
                    normalized_phrase TEXT NOT NULL,
                    intent TEXT NOT NULL,
                    action_type TEXT NOT NULL,
                    parameters TEXT,
                    confidence REAL DEFAULT 1.0,
                    usage_count INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Tabela de sinônimos aprendidos
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS synonyms (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    word TEXT NOT NULL,
                    synonym TEXT NOT NULL,
                    context TEXT,
                    confidence REAL DEFAULT 1.0
                )
            ''')
            
            # Tabela de contexto de conversa
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS conversation_context (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_input TEXT NOT NULL,
                    extracted_intent TEXT,
                    extracted_entities TEXT,
                    response_given TEXT,
                    user_feedback TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Erro ao configurar base de dados NLP: {e}")
    
    def _load_base_patterns(self):
        """Carrega padrões base de comandos"""
        base_patterns = {
            'open_application': {
                'patterns': [
                    r'abr(?:ir|a|e)\s+(?:o\s+)?(.+)',
                    r'execut(?:ar|e)\s+(?:o\s+)?(.+)',
                    r'iniciar?\s+(?:o\s+)?(.+)',
                    r'rodar?\s+(?:o\s+)?(.+)',
                    r'ligar?\s+(?:o\s+)?(.+)',
                    r'de\s+forma\s+imediata,?\s+abr(?:ir|a|e)\s+(?:o\s+)?(.+)',
                    r'por\s+favor,?\s+abr(?:ir|a|e)\s+(?:o\s+)?(.+)'
                ],
                'apps': {
                    'calculadora': ['calc', 'calculadora', 'calculator'],
                    'notepad': ['notepad', 'bloco', 'notas', 'editor'],
                    'chrome': ['chrome', 'navegador', 'browser'],
                    'opera': ['opera'],
                    'voicemod': ['voicemod', 'voice mod'],
                    'spotify': ['spotify', 'música', 'music'],
                    'whatsapp': ['whatsapp', 'whats', 'zap'],
                    'discord': ['discord'],
                    'steam': ['steam', 'jogos', 'games']
                }
            },
            'time_query': {
                'patterns': [
                    r'que\s+horas?\s+(?:são|é)',
                    r'qual\s+(?:o\s+)?horário\s+atual',
                    r'me\s+diga\s+as?\s+horas?',
                    r'horário\s+agora',
                    r'tempo\s+atual'
                ]
            },
            'math_operation': {
                'patterns': [
                    r'quanto\s+(?:é|vale)\s+(.+)',
                    r'calcul(?:ar|e)\s+(.+)',
                    r'(?:faça|fazer)\s+(?:a\s+)?conta\s+(.+)',
                    r'resolv(?:er|a)\s+(.+)',
                    r'(\d+)\s*([+\-*/x×÷])\s*(\d+)'
                ]
            },
            'volume_control': {
                'patterns': [
                    r'(?:aumentar?|subir)\s+(?:o\s+)?volume',
                    r'(?:diminuir?|baixar)\s+(?:o\s+)?volume',
                    r'(?:silenciar?|mutar?)\s+(?:o\s+)?(?:som|áudio|volume)'
                ]
            },
            'search_web': {
                'patterns': [
                    r'pesquis(?:ar|e)\s+(?:por\s+)?(.+)',
                    r'procur(?:ar|e)\s+(?:por\s+)?(.+)',
                    r'busc(?:ar|e)\s+(?:por\s+)?(.+)',
                    r'googl(?:ar|e)\s+(.+)'
                ]
            }
        }
        
        # Salvar padrões na base de dados
        for intent, data in base_patterns.items():
            self.intent_patterns[intent] = data['patterns']
            if 'apps' in data:
                self.command_patterns[intent] = data['apps']
    
    def _load_learned_patterns(self):
        """Carrega padrões aprendidos da base de dados"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT normalized_phrase, intent, action_type, parameters, confidence
                FROM command_patterns
                WHERE confidence > 0.5
                ORDER BY usage_count DESC
            ''')
            
            patterns = cursor.fetchall()
            
            for phrase, intent, action_type, params, confidence in patterns:
                if intent not in self.intent_patterns:
                    self.intent_patterns[intent] = []
                
                # Converter frase aprendida em padrão regex
                pattern = self._phrase_to_pattern(phrase)
                self.intent_patterns[intent].append(pattern)
            
            conn.close()
            self.logger.info(f"Carregados {len(patterns)} padrões aprendidos")
            
        except Exception as e:
            self.logger.error(f"Erro ao carregar padrões: {e}")
    
    def _phrase_to_pattern(self, phrase):
        """Converte frase em padrão regex flexível"""
        # Normalizar frase
        phrase = phrase.lower().strip()
        
        # Escapar caracteres especiais
        phrase = re.escape(phrase)
        
        # Tornar mais flexível
        phrase = phrase.replace(r'\ ', r'\s+')  # Espaços flexíveis
        phrase = r'.*' + phrase + r'.*'  # Permitir texto antes e depois
        
        return phrase
    
    def understand_command(self, user_input):
        """Entende comando natural do usuário"""
        try:
            self.logger.info(f"Analisando comando: {user_input}")
            
            # Normalizar entrada
            normalized_input = self._normalize_text(user_input)
            
            # Extrair intenção
            intent, confidence, entities = self._extract_intent(normalized_input)
            
            # Se não entendeu, pedir esclarecimento
            if confidence < 0.3:
                return {
                    'understood': False,
                    'intent': 'clarification_needed',
                    'confidence': confidence,
                    'response': "Não entendi o que você quer, pode me explicar para que eu possa entender?",
                    'original_input': user_input
                }
            
            # Processar comando baseado na intenção
            result = self._process_intent(intent, entities, normalized_input)
            result['understood'] = True
            result['confidence'] = confidence
            result['original_input'] = user_input
            
            # Salvar contexto
            self._save_context(user_input, intent, entities, result.get('response', ''))
            
            return result
            
        except Exception as e:
            self.logger.error(f"Erro ao entender comando: {e}")
            return {
                'understood': False,
                'intent': 'error',
                'confidence': 0.0,
                'response': "Desculpe, tive um problema para processar isso.",
                'original_input': user_input
            }
    
    def _normalize_text(self, text):
        """Normaliza texto para processamento"""
        # Converter para minúsculas
        text = text.lower().strip()
        
        # Remover pontuação desnecessária
        text = re.sub(r'[,!?;:]', ' ', text)
        
        # Normalizar espaços
        text = re.sub(r'\s+', ' ', text)
        
        # Remover palavras de cortesia
        courtesy_words = ['sara', 'por favor', 'obrigado', 'obrigada']
        for word in courtesy_words:
            text = re.sub(rf'\b{word}\b', '', text)
        
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _extract_intent(self, text):
        """Extrai intenção do texto"""
        best_intent = None
        best_confidence = 0.0
        best_entities = {}
        
        # Testar cada padrão de intenção
        for intent, patterns in self.intent_patterns.items():
            for pattern in patterns:
                try:
                    match = re.search(pattern, text, re.IGNORECASE)
                    if match:
                        # Calcular confiança baseada na qualidade do match
                        confidence = self._calculate_confidence(text, pattern, match)
                        
                        if confidence > best_confidence:
                            best_confidence = confidence
                            best_intent = intent
                            best_entities = self._extract_entities(intent, match, text)
                            
                except Exception as e:
                    self.logger.debug(f"Erro no padrão {pattern}: {e}")
                    continue
        
        return best_intent, best_confidence, best_entities
    
    def _calculate_confidence(self, text, pattern, match):
        """Calcula confiança do match"""
        # Confiança base
        confidence = 0.5
        
        # Bonus por match completo
        if match.group(0) == text:
            confidence += 0.3
        
        # Bonus por tamanho do match
        match_ratio = len(match.group(0)) / len(text)
        confidence += match_ratio * 0.2
        
        return min(confidence, 1.0)
    
    def _extract_entities(self, intent, match, text):
        """Extrai entidades do match"""
        entities = {}
        
        if intent == 'open_application':
            if match.groups():
                app_name = match.group(1).strip()
                entities['app'] = self._resolve_app_name(app_name)
        
        elif intent == 'math_operation':
            if match.groups():
                if len(match.groups()) >= 3:  # Operação completa
                    entities['num1'] = match.group(1)
                    entities['operator'] = match.group(2)
                    entities['num2'] = match.group(3)
                else:
                    entities['expression'] = match.group(1)
        
        elif intent == 'search_web':
            if match.groups():
                entities['query'] = match.group(1).strip()
        
        return entities
    
    def _resolve_app_name(self, app_name):
        """Resolve nome do aplicativo para nome padrão"""
        app_name = app_name.lower().strip()
        
        # Verificar nos padrões conhecidos
        if 'open_application' in self.command_patterns:
            for standard_name, variations in self.command_patterns['open_application'].items():
                if app_name in variations or any(var in app_name for var in variations):
                    return standard_name
        
        # Se não encontrou, retornar como está
        return app_name
    
    def _process_intent(self, intent, entities, text):
        """Processa intenção identificada"""
        if intent == 'open_application':
            return self._process_open_app(entities)
        
        elif intent == 'time_query':
            return self._process_time_query()
        
        elif intent == 'math_operation':
            return self._process_math(entities)
        
        elif intent == 'volume_control':
            return self._process_volume(text)
        
        elif intent == 'search_web':
            return self._process_search(entities)
        
        else:
            return {
                'intent': intent,
                'action': None,
                'response': "Entendi sua intenção, mas ainda não sei como executar isso."
            }
    
    def _process_open_app(self, entities):
        """Processa comando de abrir aplicativo"""
        app = entities.get('app', 'unknown')
        
        return {
            'intent': 'open_application',
            'action': 'open_application',
            'parameters': {'app': app},
            'response': f"Perfeito! Vou abrir o {app} para você agora mesmo!"
        }
    
    def _process_time_query(self):
        """Processa consulta de horário"""
        now = datetime.now()
        time_str = now.strftime('%H:%M')
        date_str = now.strftime('%d de %B de %Y')
        
        return {
            'intent': 'time_query',
            'action': 'get_time',
            'parameters': {'time': time_str, 'date': date_str},
            'response': f"Agora são {time_str} do dia {date_str}."
        }
    
    def _process_math(self, entities):
        """Processa operação matemática"""
        try:
            if 'num1' in entities and 'operator' in entities and 'num2' in entities:
                num1 = float(entities['num1'])
                num2 = float(entities['num2'])
                op = entities['operator']
                
                if op in ['+', 'mais']:
                    result = num1 + num2
                elif op in ['-', 'menos']:
                    result = num1 - num2
                elif op in ['*', 'x', '×', 'vezes']:
                    result = num1 * num2
                elif op in ['/', '÷', 'dividido']:
                    result = num1 / num2 if num2 != 0 else "infinito"
                else:
                    return {
                        'intent': 'math_operation',
                        'action': None,
                        'response': "Não reconheço essa operação matemática."
                    }
                
                return {
                    'intent': 'math_operation',
                    'action': 'calculate',
                    'parameters': {'expression': f"{num1} {op} {num2}", 'result': result},
                    'response': f"{num1} {op} {num2} é igual a {result}"
                }
            
            elif 'expression' in entities:
                # Tentar avaliar expressão mais complexa
                expr = entities['expression']
                # Por segurança, apenas operações básicas
                if re.match(r'^[\d+\-*/\s().]+$', expr):
                    try:
                        result = eval(expr)
                        return {
                            'intent': 'math_operation',
                            'action': 'calculate',
                            'parameters': {'expression': expr, 'result': result},
                            'response': f"{expr} é igual a {result}"
                        }
                    except:
                        pass
                
                return {
                    'intent': 'math_operation',
                    'action': None,
                    'response': "Não consegui resolver essa operação. Pode reformular?"
                }
        
        except Exception as e:
            self.logger.error(f"Erro no cálculo: {e}")
        
        return {
            'intent': 'math_operation',
            'action': None,
            'response': "Não consegui fazer esse cálculo. Pode tentar de outra forma?"
        }
    
    def _process_volume(self, text):
        """Processa controle de volume"""
        if any(word in text for word in ['aumentar', 'subir']):
            return {
                'intent': 'volume_control',
                'action': 'volume_up',
                'parameters': {},
                'response': "Aumentando o volume para você!"
            }
        elif any(word in text for word in ['diminuir', 'baixar']):
            return {
                'intent': 'volume_control',
                'action': 'volume_down',
                'parameters': {},
                'response': "Diminuindo o volume!"
            }
        else:
            return {
                'intent': 'volume_control',
                'action': 'volume_mute',
                'parameters': {},
                'response': "Silenciando o áudio!"
            }
    
    def _process_search(self, entities):
        """Processa pesquisa web"""
        query = entities.get('query', '')
        
        return {
            'intent': 'search_web',
            'action': 'web_search',
            'parameters': {'query': query},
            'response': f"Vou pesquisar '{query}' para você!"
        }
    
    def learn_new_pattern(self, user_input, intended_action, action_params=None):
        """Aprende novo padrão de comando"""
        try:
            normalized = self._normalize_text(user_input)
            
            # Salvar na base de dados
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO command_patterns 
                (original_phrase, normalized_phrase, intent, action_type, parameters)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_input, normalized, intended_action, intended_action, 
                  json.dumps(action_params) if action_params else None))
            
            conn.commit()
            conn.close()
            
            # Atualizar padrões em memória
            pattern = self._phrase_to_pattern(normalized)
            if intended_action not in self.intent_patterns:
                self.intent_patterns[intended_action] = []
            self.intent_patterns[intended_action].append(pattern)
            
            self.logger.info(f"Novo padrão aprendido: {user_input} -> {intended_action}")
            return True
            
        except Exception as e:
            self.logger.error(f"Erro ao aprender padrão: {e}")
            return False
    
    def _save_context(self, user_input, intent, entities, response):
        """Salva contexto da conversa"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO conversation_context 
                (user_input, extracted_intent, extracted_entities, response_given)
                VALUES (?, ?, ?, ?)
            ''', (user_input, intent, json.dumps(entities), response))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Erro ao salvar contexto: {e}")
    
    def get_learning_stats(self):
        """Retorna estatísticas de aprendizado"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM command_patterns')
            learned_patterns = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM conversation_context')
            conversations = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(*) FROM synonyms')
            synonyms = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'learned_patterns': learned_patterns,
                'conversations': conversations,
                'synonyms': synonyms,
                'intents_available': len(self.intent_patterns)
            }
            
        except Exception as e:
            self.logger.error(f"Erro ao obter estatísticas: {e}")
            return {}
