/* SARA AI - Estilos do Painel de Controle */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2.5rem;
    color: #667eea;
}

.logo h1 {
    font-size: 2rem;
    color: #333;
    font-weight: 700;
}

.version {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}

.status-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #dc3545;
    animation: pulse 2s infinite;
}

.status-dot.connected {
    background: #28a745;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Main Content */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* Cards */
.card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.card-header i {
    font-size: 1.5rem;
    color: #667eea;
}

.card-header h3 {
    font-size: 1.3rem;
    color: #333;
    font-weight: 600;
}

.full-width {
    width: 100%;
}

/* Dashboard */
.dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.status-item:last-child {
    border-bottom: none;
}

.status-value {
    font-weight: 600;
    color: #667eea;
}

/* Control Panel */
.command-input {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
}

.command-input input {
    flex: 1;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.command-input input:focus {
    outline: none;
    border-color: #667eea;
}

.quick-commands {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

/* Buttons */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #667eea;
    color: white;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #f8f9fa;
    color: #333;
    border: 2px solid #e0e0e0;
}

.btn-secondary:hover {
    background: #e9ecef;
    border-color: #667eea;
}

.btn-small {
    padding: 8px 15px;
    font-size: 0.85rem;
    margin-left: auto;
}

/* Conversation History */
.conversation-history {
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
}

.conversation-item {
    margin-bottom: 15px;
    padding: 15px;
    border-radius: 10px;
    animation: fadeIn 0.3s ease;
}

.conversation-item.user {
    background: #e3f2fd;
    margin-left: 20px;
}

.conversation-item.assistant {
    background: #f3e5f5;
    margin-right: 20px;
}

.conversation-item.system {
    background: #fff3e0;
    text-align: center;
}

.message-content .timestamp {
    font-size: 0.8rem;
    color: #666;
    font-weight: 500;
}

.message-content p {
    margin-top: 5px;
    line-height: 1.5;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Configuration */
.config-section {
    margin-bottom: 25px;
}

.config-section h4 {
    color: #333;
    margin-bottom: 15px;
    font-weight: 600;
}

.config-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.config-item label {
    min-width: 150px;
    font-weight: 500;
}

.config-item input[type="range"] {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #e0e0e0;
    outline: none;
}

.config-item input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
}

.test-buttons {
    display: flex;
    gap: 15px;
}

/* Logs */
.logs-container {
    max-height: 300px;
    overflow-y: auto;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.log-entry {
    display: flex;
    gap: 15px;
    padding: 8px 0;
    border-bottom: 1px solid #e0e0e0;
}

.log-entry:last-child {
    border-bottom: none;
}

.log-time {
    color: #666;
    min-width: 80px;
}

.log-level {
    min-width: 60px;
    font-weight: 600;
}

.log-level.INFO { color: #17a2b8; }
.log-level.WARNING { color: #ffc107; }
.log-level.ERROR { color: #dc3545; }
.log-level.DEBUG { color: #6c757d; }

.log-message {
    flex: 1;
}

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .dashboard {
        grid-template-columns: 1fr;
    }
    
    .command-input {
        flex-direction: column;
    }
    
    .quick-commands {
        justify-content: center;
    }
    
    .config-item {
        flex-direction: column;
        align-items: stretch;
    }
    
    .test-buttons {
        flex-direction: column;
    }
}
