"""
SARA AI - Sistema de Treinamento
Interface simples para treinar e corrigir a IA
"""

import json
import os
from datetime import datetime
from src.logger import setup_logger

class TrainingSystem:
    """Sistema para treinar e corrigir a SARA AI"""
    
    def __init__(self, ai_brain):
        self.logger = setup_logger(__name__)
        self.ai_brain = ai_brain
        self.training_file = "data/training_corrections.json"
        self.corrections = []
        
        # Carregar correções existentes
        self._load_corrections()
        
    def _load_corrections(self):
        """Carrega correções salvas"""
        try:
            if os.path.exists(self.training_file):
                with open(self.training_file, 'r', encoding='utf-8') as f:
                    self.corrections = json.load(f)
                self.logger.info(f"Carregadas {len(self.corrections)} correções")
        except Exception as e:
            self.logger.error(f"Erro ao carregar correções: {e}")
            self.corrections = []
    
    def _save_corrections(self):
        """Salva correções no arquivo"""
        try:
            os.makedirs(os.path.dirname(self.training_file), exist_ok=True)
            with open(self.training_file, 'w', encoding='utf-8') as f:
                json.dump(self.corrections, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"Erro ao salvar correções: {e}")
    
    def correct_response(self, user_input, wrong_response, correct_response, explanation=""):
        """Corrige uma resposta da IA"""
        try:
            correction = {
                'id': len(self.corrections) + 1,
                'user_input': user_input,
                'wrong_response': wrong_response,
                'correct_response': correct_response,
                'explanation': explanation,
                'timestamp': datetime.now().isoformat(),
                'applied': False
            }
            
            self.corrections.append(correction)
            self._save_corrections()
            
            # Aplicar correção na IA
            success = self.ai_brain.learn_from_correction(
                user_input, correct_response, "user_correction"
            )
            
            if success:
                correction['applied'] = True
                self._save_corrections()
                self.logger.info(f"Correção aplicada: {user_input}")
                return True, "Correção aplicada com sucesso! A SARA vai lembrar disso."
            else:
                return False, "Erro ao aplicar correção."
                
        except Exception as e:
            self.logger.error(f"Erro na correção: {e}")
            return False, f"Erro: {e}"
    
    def teach_new_command(self, command_phrase, action_type, parameters=None, description=""):
        """Ensina um novo comando para a IA"""
        try:
            # Adicionar comando personalizado
            success = self.ai_brain.add_custom_command(command_phrase, action_type, parameters)
            
            if success:
                # Salvar no histórico de treinamento
                training_entry = {
                    'type': 'new_command',
                    'command_phrase': command_phrase,
                    'action_type': action_type,
                    'parameters': parameters,
                    'description': description,
                    'timestamp': datetime.now().isoformat()
                }
                
                self.corrections.append(training_entry)
                self._save_corrections()
                
                self.logger.info(f"Novo comando ensinado: {command_phrase}")
                return True, f"Comando '{command_phrase}' ensinado com sucesso!"
            else:
                return False, "Erro ao ensinar novo comando."
                
        except Exception as e:
            self.logger.error(f"Erro ao ensinar comando: {e}")
            return False, f"Erro: {e}"
    
    def get_training_suggestions(self):
        """Retorna sugestões de treinamento baseadas no uso"""
        try:
            stats = self.ai_brain.get_learning_stats()
            suggestions = []
            
            # Sugestões baseadas em estatísticas
            if stats.get('user_corrections', 0) < 5:
                suggestions.append({
                    'type': 'correction',
                    'title': 'Corrija Respostas',
                    'description': 'Quando a SARA responder algo errado, use o comando "SARA, isso está errado. A resposta correta é: [sua resposta]"'
                })
            
            if stats.get('learned_commands', 0) < 3:
                suggestions.append({
                    'type': 'command',
                    'title': 'Ensine Novos Comandos',
                    'description': 'Ensine comandos personalizados: "SARA, aprenda: quando eu disser [frase], você deve [ação]"'
                })
            
            # Comandos úteis para ensinar
            useful_commands = [
                {
                    'phrase': 'abrir whatsapp',
                    'action': 'open_application',
                    'params': {'app': 'whatsapp'},
                    'description': 'Abrir WhatsApp Desktop'
                },
                {
                    'phrase': 'abrir spotify',
                    'action': 'open_application', 
                    'params': {'app': 'spotify'},
                    'description': 'Abrir Spotify'
                },
                {
                    'phrase': 'desligar computador',
                    'action': 'shutdown_system',
                    'params': {'delay': 60},
                    'description': 'Desligar o computador'
                }
            ]
            
            suggestions.append({
                'type': 'examples',
                'title': 'Comandos Úteis para Ensinar',
                'commands': useful_commands
            })
            
            return suggestions
            
        except Exception as e:
            self.logger.error(f"Erro ao gerar sugestões: {e}")
            return []
    
    def get_training_history(self):
        """Retorna histórico de treinamento"""
        return self.corrections.copy()
    
    def export_training_data(self, filename=None):
        """Exporta dados de treinamento"""
        try:
            if not filename:
                filename = f"sara_training_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            export_data = {
                'export_date': datetime.now().isoformat(),
                'total_corrections': len(self.corrections),
                'corrections': self.corrections,
                'ai_stats': self.ai_brain.get_learning_stats()
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            return True, f"Dados exportados para {filename}"
            
        except Exception as e:
            self.logger.error(f"Erro na exportação: {e}")
            return False, f"Erro: {e}"
    
    def import_training_data(self, filename):
        """Importa dados de treinamento"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                import_data = json.load(f)
            
            imported_corrections = import_data.get('corrections', [])
            
            # Aplicar correções importadas
            applied_count = 0
            for correction in imported_corrections:
                if correction.get('type') == 'new_command':
                    success = self.ai_brain.add_custom_command(
                        correction['command_phrase'],
                        correction['action_type'],
                        correction.get('parameters')
                    )
                    if success:
                        applied_count += 1
                else:
                    success = self.ai_brain.learn_from_correction(
                        correction['user_input'],
                        correction['correct_response'],
                        "imported_correction"
                    )
                    if success:
                        applied_count += 1
            
            # Adicionar ao histórico local
            self.corrections.extend(imported_corrections)
            self._save_corrections()
            
            return True, f"Importadas {applied_count} correções de {len(imported_corrections)} total"
            
        except Exception as e:
            self.logger.error(f"Erro na importação: {e}")
            return False, f"Erro: {e}"
    
    def quick_train_commands(self):
        """Treinamento rápido com comandos comuns"""
        common_commands = [
            {
                'phrase': 'que dia é hoje',
                'response': 'Hoje é {current_date}',
                'type': 'date_query'
            },
            {
                'phrase': 'como está o tempo',
                'response': 'Vou abrir um site de meteorologia para você!',
                'action': 'open_website',
                'params': {'url': 'weather.com'}
            },
            {
                'phrase': 'toque uma música',
                'response': 'Tocando música para você!',
                'action': 'play_music'
            },
            {
                'phrase': 'pare a música',
                'response': 'Pausando a música!',
                'action': 'pause_music'
            }
        ]
        
        trained_count = 0
        for cmd in common_commands:
            try:
                if 'action' in cmd:
                    success = self.ai_brain.add_custom_command(
                        cmd['phrase'],
                        cmd['action'],
                        cmd.get('params', {})
                    )
                else:
                    success = self.ai_brain.learn_from_correction(
                        cmd['phrase'],
                        cmd['response'],
                        "quick_training"
                    )
                
                if success:
                    trained_count += 1
                    
            except Exception as e:
                self.logger.error(f"Erro no treinamento rápido: {e}")
        
        return trained_count, len(common_commands)
    
    def get_performance_metrics(self):
        """Retorna métricas de performance da IA"""
        try:
            stats = self.ai_brain.get_learning_stats()
            
            # Calcular taxa de sucesso
            total_interactions = stats.get('total_interactions', 0)
            corrections = stats.get('user_corrections', 0)
            
            success_rate = ((total_interactions - corrections) / total_interactions * 100) if total_interactions > 0 else 0
            
            metrics = {
                'success_rate': round(success_rate, 2),
                'total_interactions': total_interactions,
                'learned_commands': stats.get('learned_commands', 0),
                'corrections_applied': corrections,
                'ml_enabled': stats.get('ml_available', False),
                'learning_enabled': stats.get('learning_enabled', True)
            }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Erro ao calcular métricas: {e}")
            return {}
    
    def reset_training(self):
        """Reseta todo o treinamento (usar com cuidado)"""
        try:
            # Backup antes de resetar
            backup_file = f"training_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            self.export_training_data(backup_file)
            
            # Limpar correções
            self.corrections = []
            self._save_corrections()
            
            # Limpar base de dados da IA (se implementado)
            # self.ai_brain.reset_learning_data()
            
            return True, f"Treinamento resetado. Backup salvo em {backup_file}"
            
        except Exception as e:
            self.logger.error(f"Erro ao resetar treinamento: {e}")
            return False, f"Erro: {e}"
