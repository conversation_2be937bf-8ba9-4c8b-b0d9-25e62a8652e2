# SARA AI - Assistente de IA Conversacional (Versão Independente)
# Dependências completas e atualizadas

# Ferramentas de build (resolver erro setuptools)
setuptools>=65.0.0
wheel>=0.37.0
pip>=22.0.0

# Reconhecimento e síntese de voz
SpeechRecognition>=3.10.0
pyttsx3>=2.90
pyaudio>=0.2.11

# Interface web e API
Flask>=2.3.0
Flask-CORS>=4.0.0
Flask-SocketIO>=5.3.6
python-socketio>=5.9.0

# Controle de sistema e automação
pyautogui>=0.9.54
psutil>=5.9.6
keyboard>=0.13.5
mouse>=0.7.1

# Utilitários
python-dotenv>=1.0.0
requests>=2.31.0
numpy>=1.24.3

# Logging e monitoramento
colorlog>=6.7.0
watchdog>=3.0.0

# Processamento de texto e IA local
nltk>=3.8.1
textblob>=0.17.1
scikit-learn>=1.3.0
pandas>=2.0.0

# Síntese de voz avançada
edge-tts>=6.1.0
gTTS>=2.3.0

# Base de dados para aprendizado
# sqlite3 (built-in no Python)
json5>=0.9.14
pyyaml>=6.0.1

# Threading e async
asyncio
threading2>=0.1.2
