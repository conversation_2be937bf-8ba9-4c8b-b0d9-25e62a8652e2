# SARA AI - Assistente de IA Conversacional (Versão Independente)
# Dependências essenciais apenas

# Ferramentas de build (resolver erro setuptools)
setuptools>=65.0.0
wheel>=0.37.0
pip>=22.0.0

# Reconhecimento e síntese de voz
SpeechRecognition>=3.8.1
pyttsx3>=2.90

# Interface web e API (versões estáveis)
Flask>=2.3.0
Flask-CORS>=4.0.0

# Controle de sistema e automação
pyautogui>=0.9.50
psutil>=5.8.0

# Utilitários básicos
python-dotenv>=0.19.0
requests>=2.28.0

# Logging
colorlog>=6.6.0

# Processamento de texto local (sem IA externa)
nltk>=3.8
textblob>=0.17.1

# PyAudio alternativo (mais compatível)
# Comentado - será instalado separadamente se necessário
# pyaudio>=0.2.11
